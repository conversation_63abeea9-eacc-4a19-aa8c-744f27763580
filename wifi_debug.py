"""
WiFi Debug Tool für ESP32
Hilft bei der Diagnose von WiFi-Verbindungsproblemen
"""

import time
try:
    import network
    import machine
    import config
except ImportError:
    print("Dieses Script muss auf dem ESP32 ausgeführt werden!")
    exit()


def scan_networks():
    """Scannt verfügbare WiFi-Netzwerke"""
    print("=== WiFi Network Scan ===")
    
    sta_if = network.WLAN(network.STA_IF)
    sta_if.active(True)
    time.sleep(1)
    
    print("Scanning...")
    networks = sta_if.scan()
    
    if not networks:
        print("Keine Netzwerke gefunden!")
        return []
    
    print(f"Gefunden: {len(networks)} Netzwerke")
    print()
    
    # Sortieren nach Signalstärke
    networks.sort(key=lambda x: x[3], reverse=True)
    
    print("SSID                     | Signal | Kanal | Sicherheit")
    print("-" * 55)
    
    for net in networks:
        ssid = net[0].decode('utf-8')
        bssid = ':'.join(['%02x' % b for b in net[1]])
        channel = net[2]
        rssi = net[3]
        authmode = net[4]
        hidden = net[5]
        
        # Sicherheits-Typ
        auth_types = {
            0: 'Open',
            1: 'WEP', 
            2: 'WPA-PSK',
            3: 'WPA2-PSK',
            4: 'WPA/WPA2-PSK',
            5: 'WPA2-Enterprise'
        }
        
        auth_text = auth_types.get(authmode, f'Unknown({authmode})')
        
        # Signalstärke bewerten
        if rssi > -50:
            signal_quality = "Excellent"
        elif rssi > -60:
            signal_quality = "Good"
        elif rssi > -70:
            signal_quality = "Fair"
        else:
            signal_quality = "Poor"
        
        print(f"{ssid:<24} | {rssi:>4} dBm | {channel:>5} | {auth_text}")
        
        # Unser Ziel-Netzwerk markieren
        if ssid == config.SSID:
            print(f"  ★ TARGET NETWORK - Signal: {signal_quality}")
    
    return networks


def test_wifi_connection():
    """Testet WiFi-Verbindung mit detailliertem Logging"""
    print("\n=== WiFi Connection Test ===")
    
    print(f"Target SSID: '{config.SSID}'")
    print(f"Password length: {len(config.PASSWORD)} characters")
    
    sta_if = network.WLAN(network.STA_IF)
    
    # Aktuellen Status prüfen
    if sta_if.isconnected():
        print("Already connected!")
        print(f"IP: {sta_if.ifconfig()[0]}")
        return True
    
    # WiFi aktivieren
    print("Activating WiFi...")
    sta_if.active(True)
    time.sleep(2)
    
    # Verbindung versuchen
    print("Attempting connection...")
    sta_if.connect(config.SSID, config.PASSWORD)
    
    # Status überwachen
    for attempt in range(30):  # 30 Sekunden
        status = sta_if.status()
        
        status_names = {
            0: 'IDLE',
            1: 'CONNECTING',
            2: 'WRONG_PASSWORD', 
            3: 'NO_AP_FOUND',
            4: 'CONNECT_FAIL',
            5: 'GOT_IP'
        }
        
        status_name = status_names.get(status, f'UNKNOWN({status})')
        print(f"Attempt {attempt + 1}/30: Status = {status} ({status_name})")
        
        if sta_if.isconnected():
            print("✓ Connection successful!")
            config_info = sta_if.ifconfig()
            print(f"IP Address: {config_info[0]}")
            print(f"Subnet Mask: {config_info[1]}")
            print(f"Gateway: {config_info[2]}")
            print(f"DNS: {config_info[3]}")
            return True
        
        if status == 2:  # Wrong password
            print("✗ Wrong password!")
            return False
        elif status == 3:  # No AP found
            print("✗ Network not found!")
            return False
        elif status == 4:  # Connect fail
            print("✗ Connection failed!")
            return False
        
        time.sleep(1)
    
    print("✗ Connection timeout!")
    return False


def check_config():
    """Überprüft die Konfiguration"""
    print("=== Configuration Check ===")
    
    print(f"SSID: '{config.SSID}'")
    print(f"SSID length: {len(config.SSID)} characters")
    print(f"Password length: {len(config.PASSWORD)} characters")
    
    # Häufige Probleme prüfen
    issues = []
    
    if config.SSID in ['SSID', 'IHR_WIFI_NAME', 'IhrWiFiName']:
        issues.append("SSID not configured (still default value)")

    if config.PASSWORD in ['PASSWORD', 'IHR_WIFI_PASSWORT', 'IhrWiFiPasswort']:
        issues.append("Password not configured (still default value)")
    
    if len(config.SSID) == 0:
        issues.append("SSID is empty")
    
    if len(config.PASSWORD) == 0:
        issues.append("Password is empty")
    
    if len(config.PASSWORD) < 8:
        issues.append("Password too short (WPA requires min 8 characters)")
    
    # Sonderzeichen prüfen
    if any(ord(c) > 127 for c in config.SSID):
        issues.append("SSID contains non-ASCII characters")
    
    if any(ord(c) > 127 for c in config.PASSWORD):
        issues.append("Password contains non-ASCII characters")
    
    if issues:
        print("\n⚠️  Configuration Issues:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✓ Configuration looks good")
        return True


def system_info():
    """Zeigt System-Informationen"""
    print("=== System Information ===")
    
    print(f"Platform: {machine.unique_id().hex()}")
    print(f"Frequency: {machine.freq()} Hz")
    
    # Memory info
    import gc
    gc.collect()
    print(f"Free memory: {gc.mem_free()} bytes")
    print(f"Allocated memory: {gc.mem_alloc()} bytes")


def main():
    """Hauptfunktion für WiFi-Debugging"""
    print("ESP32 WiFi Debug Tool")
    print("=" * 50)
    
    # System-Info
    system_info()
    print()
    
    # Konfiguration prüfen
    config_ok = check_config()
    print()
    
    if not config_ok:
        print("❌ Please fix configuration issues first!")
        return
    
    # Netzwerke scannen
    networks = scan_networks()
    print()
    
    # Prüfen ob Ziel-Netzwerk gefunden wurde
    target_found = False
    for net in networks:
        if net[0].decode('utf-8') == config.SSID:
            target_found = True
            break
    
    if not target_found:
        print(f"❌ Target network '{config.SSID}' not found!")
        print("Possible solutions:")
        print("1. Check SSID spelling in config.py")
        print("2. Make sure network is 2.4GHz (not 5GHz)")
        print("3. Move closer to WiFi router")
        print("4. Check if network is hidden")
        return
    
    # Verbindung testen
    success = test_wifi_connection()
    
    if success:
        print("\n🎉 WiFi connection successful!")
        print("You can now run the main TCPI server.")
    else:
        print("\n❌ WiFi connection failed!")
        print("Troubleshooting steps:")
        print("1. Double-check password in config.py")
        print("2. Try connecting with phone/laptop first")
        print("3. Check router settings (WPA2, not WPA3)")
        print("4. Restart ESP32 and try again")


if __name__ == "__main__":
    main()
