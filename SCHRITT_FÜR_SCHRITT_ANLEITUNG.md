# Schritt-für-Schritt Anleitung: EEPROM-Programmierung

## Vorbereitung

### 1. WiFi-Konfiguration anpassen

**WICHTIG**: Bearbeiten Sie `config.py` und tragen Sie Ihre WiFi-Daten ein:

```python
SSID = 'IHR_WIFI_NAME'        # <-- Hier Ihr WiFi-Name
PASSWORD = 'IHR_WIFI_PASSWORT'  # <-- Hier <PERSON>hr WiFi-Passwort
```

### 2. Dateien auf ESP32 kopieren

Kopieren Sie diese Dateien auf Ihren ESP32:
- `main.py` (aktiviert)
- `config.py` (mit WiFi-Daten)
- `test_tcpi_upy.py` (erweitert)
- `eeprom_upy.py` (neu)

## Test-Phase 1: ESP32 Grundfunktion

### Schritt 1: ESP32 starten und Logs prüfen

1. **ESP32 neu starten** (Reset-<PERSON><PERSON> oder Stromversorgung)

2. **Serielle Konsole öffnen** (<PERSON><PERSON><PERSON><PERSON>, <PERSON>u<PERSON>, oder Arduino IDE Serial Monitor)
   - Baudrate: 115200
   - Port: Ihr ESP32-Port

3. **Erwartete Ausgabe prüfen:**
```
connecting to network...
Network configuration: ('192.168.1.XXX', '*************', '***********', '*******')
EEPROM functionality loaded
EEPROM Manager initialized at address 0x50
[Server listening on 0.0.0.0:8086]
```

**WICHTIG**: Notieren Sie sich die IP-Adresse (z.B. 192.168.1.XXX)

### Schritt 2: Netzwerk-Verbindung testen

```bash
# Ping-Test
ping 192.168.1.XXX

# Port-Test (Windows)
telnet 192.168.1.XXX 8086

# Port-Test (Linux/Mac)
nc -zv 192.168.1.XXX 8086
```

**Erwartetes Ergebnis**: Verbindung erfolgreich

## Test-Phase 2: EEPROM-Funktionalität

### Schritt 3: Einfacher Verbindungstest

```bash
python eeprom_programmer.py --test --host 192.168.1.XXX
```

**Erwartete Ausgabe:**
```
Verbinde zu 192.168.1.XXX:8086...
Verbindung hergestellt!
Schreibe 8 Bytes ab Adresse 0x0000
✓ EEPROM-Schreibvorgang erfolgreich!
Verbindung getrennt.
✓ Alle Operationen erfolgreich abgeschlossen!
```

**ESP32-Logs sollten zeigen:**
```
EEPROM Write: 0x0000, 8 bytes
  Page 0x0000: 8 bytes
Verifying...
Verification OK!
```

### Schritt 4: HEX-Datei Test

```bash
python eeprom_programmer.py --hex-file test_sigma.hex --host 192.168.1.XXX
```

**Erwartete Ausgabe:**
```
Verbinde zu 192.168.1.XXX:8086...
Verbindung hergestellt!
Lade HEX-Datei: test_sigma.hex
Größe: 2517 Zeichen
Sende HEX-Daten an EEPROM...
✓ EEPROM erfolgreich programmiert!
Verbindung getrennt.
✓ Alle Operationen erfolgreich abgeschlossen!
```

## Test-Phase 3: Sigma Studio Integration

### Schritt 5: Sigma Studio HEX-Export testen

1. **Sigma Studio öffnen**
2. **Beliebiges Projekt laden** (oder neues erstellen)
3. **Action → Export System Files**
4. **"Export for Standalone" wählen**
5. **Speichern als "MyProject.hex"**

### Schritt 6: Echte HEX-Datei programmieren

```bash
python eeprom_programmer.py --hex-file MyProject.hex --host 192.168.1.XXX --reset-dsp
```

**Mit DSP-Reset nach Programmierung für Self-Boot Test**

## Troubleshooting

### Problem: "EEPROM functionality not available"

**Lösung:**
1. Prüfen Sie, ob `eeprom_upy.py` auf dem ESP32 ist
2. ESP32 neu starten
3. Serielle Logs prüfen

### Problem: "Verbindungsfehler"

**Lösungsschritte:**
1. **IP-Adresse prüfen**: `ping 192.168.1.XXX`
2. **WiFi-Verbindung prüfen**: ESP32-Logs kontrollieren
3. **Port prüfen**: `telnet 192.168.1.XXX 8086`
4. **Firewall prüfen**: Windows Firewall temporär deaktivieren

### Problem: "PROGRAM_HEX_ERROR"

**Debugging:**
1. **Debug-Modus aktivieren** (ESP32-Code):
   ```python
   tcpi_server.DEBUG_MODE = True
   ```
2. **HEX-Datei prüfen**: Ist sie von Sigma Studio?
3. **I2C-Verkabelung prüfen**: SCL=Pin18, SDA=Pin5
4. **EEPROM-Stromversorgung prüfen**: 3.3V oder 5V je nach Typ

## Erweiterte Tests

### EEPROM-Dump (Inhalt auslesen)

Erstellen Sie eine Datei `eeprom_dump.py`:

```python
import socket

def dump_eeprom(host, address=0x0000, length=256):
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.connect((host, 8086))
    
    command = f"EEPROM_DUMP:{address:04X}:{length}".encode('utf-8')
    sock.send(command)
    
    response = sock.recv(1024)
    print(f"Response: {response}")
    
    sock.close()

# Verwendung
dump_eeprom('192.168.1.XXX')
```

### Direkter EEPROM-Write Test

```python
import socket

def write_test_pattern(host):
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.connect((host, 8086))
    
    # Test-Pattern: 0x01, 0x02, 0x03, 0x04
    command = "EEPROM_WRITE:0000:01020304".encode('utf-8')
    sock.send(command)
    
    response = sock.recv(1024)
    print(f"Response: {response}")
    
    sock.close()

# Verwendung
write_test_pattern('192.168.1.XXX')
```

## Erfolgs-Kriterien

### ✅ Test erfolgreich wenn:

1. **ESP32 startet** mit korrekten Log-Meldungen
2. **Netzwerk-Verbindung** funktioniert
3. **Test-Modus** läuft ohne Fehler
4. **HEX-Programmierung** zeigt "OK"
5. **DSP-Reset** funktioniert
6. **EEPROM-Dump** zeigt geschriebene Daten

### 🎯 Ziel erreicht wenn:

- Sigma Studio HEX-Dateien können programmiert werden
- DSP startet automatisch vom EEPROM (Self-Boot)
- Live-Parameter-Änderungen funktionieren weiterhin
- EEPROM-Programmierung ist reproduzierbar

## Nächste Schritte nach erfolgreichem Test

1. **Automatisierung**: Batch-Scripts für häufige Operationen
2. **Integration**: In Ihre Entwicklungsumgebung einbinden
3. **Backup**: EEPROM-Inhalte vor Änderungen sichern
4. **Dokumentation**: Ihre spezifischen Projekte dokumentieren
