Analog Devices Graphical Compiler Tool for Sigma DSP build date =  2008
  
  
Done Reading Nodelist ...  
  
Done reading file, 
  
Done Reading Parameter list ...  
  
  
################## Summary ########################
(Note: Estimates are based on a 48 kHz sample rate)
  
Number of instructions used (out of a possible 1024 ) = 602
  
Data RAM used (out of a possible 2048 ) = 335
  
Parameter RAM used (out of a possible 1024 ) = 295
  
Files written:  
  
program_data.dat - load file for downloading code using ADI loader
  
hex_program_data.dat - load file for downloading code using microcontroller 
  
spi_params.dat - file of parameter values for each instance, used by gen_spi program to make download file
  
spi_map.dat - Parameter RAM locations for each schematic instance
  
trap.dat - lists the values to enter in the trap registers to output a signal to the data-capture output pin.
  
