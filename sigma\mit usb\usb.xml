﻿<?xml version="1.0" encoding="utf-8"?>
<!-- *
 * This software is distributed in the hope that it will be useful,
 * but is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, without even the implied warranty of
 * ME<PERSON>HANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 *
 * This software may only be used to program products purchased from
 * Analog Devices for incorporation by you into audio products that
 * are intended for resale to audio product end users. This software
 * may not be distributed whole or in any part to third parties.
 *
 * Copyright ©2025 Analog Devices, Inc. All rights reserved.
 */
-->
<!--SigmaStudio export XML file-->
<Schematic>
    <IC>
        <Name>IC 2</Name>
    </IC>
    <IC>
        <Name>IC 1</Name>
        <PartNumber>Sigma100</PartNumber>
        <Register>
            <Name>IC 1.CoreRegister</Name>
            <Address>2076</Address>
            <AddrIncr>0</AddrIncr>
            <Size>2</Size>
            <Data>0x00, 0x18, </Data>
        </Register>
        <Program>
            <Name>Program Data</Name>
            <Address>1024</Address>
            <AddrIncr>5</AddrIncr>
            <Size>5120</Size>
            <Data>0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0xE8, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x08, 0x00, 0xE8, 0x01, 0xFF, 0xF6, 0x15, 0x20, 0x01, 0x00, 0x10, 0x00, 0xE2, 0x01, 0xFF, 0xF6, 0x17, 0x20, 0x01, 0x00, 0x18, 0x00, 0xE2, 0x01, 0xFF, 0xF6, 0x18, 0x20, 0x01, 0x00, 0x20, 0x00, 0xE2, 0x01, 0xFF, 0xF6, 0x16, 0x20, 0x01, 0x00, 0x28, 0x00, 0xE2, 0x01, 0xFF, 0xF2, 0x00, 0x20, 0x01, 0x00, 0x30, 0x00, 0xE2, 0x01, 0x00, 0x29, 0x08, 0x20, 0x01, 0x00, 0x40, 0x00, 0xE2, 0x01, 0x00, 0x40, 0x00, 0xC0, 0x01, 0x00, 0x37, 0xFF, 0x20, 0x01, 0x00, 0x38, 0x00, 0xE2, 0x01, 0x00, 0x02, 0x01, 0x20, 0x01, 0x00, 0x48, 0x00, 0xE2, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x0A, 0x02, 0x20, 0x01, 0x00, 0x50, 0x00, 0xE2, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x11, 0x08, 0x20, 0x01, 0x00, 0x69, 0x08, 0x22, 0x41, 0x00, 0x88, 0x00, 0xE2, 0x01, 0x00, 0x79, 0x08, 0x20, 0x01, 0x00, 0x69, 0x08, 0x34, 0x01, 0x00, 0x8A, 0x03, 0x22, 0x01, 0x00, 0x70, 0x00, 0xE2, 0x01, 0x00, 0x70, 0x00, 0xC0, 0x01, 0x00, 0x80, 0x00, 0xF2, 0x01, 0x00, 0x4F, 0xFF, 0x20, 0x01, 0x00, 0x58, 0x00, 0xE2, 0x01, 0x00, 0x57, 0xFF, 0x20, 0x01, 0x00, 0x60, 0x00, 0xE2, 0x01, 0xFF, 0xF2, 0x04, 0x20, 0x01, 0x00, 0xA1, 0x08, 0x22, 0x41, 0x00, 0xC0, 0x00, 0xE2, 0x01, 0x00, 0xB1, 0x08, 0x20, 0x01, 0x00, 0xA1, 0x08, 0x34, 0x01, 0x00, 0xC2, 0x05, 0x22, 0x01, 0x00, 0xA8, 0x00, 0xE2, 0x01, 0x00, 0xA8, 0x00, 0xC0, 0x01, 0x00, 0xB8, 0x00, 0xF2, 0x01, 0x00, 0x5F, 0xFF, 0x20, 0x01, 0x00, 0x90, 0x00, 0xE2, 0x01, 0x00, 0x67, 0xFF, 0x20, 0x01, 0x00, 0x98, 0x00, 0xE2, 0x01, 0x00, 0x91, 0x09, 0x20, 0x01, 0x00, 0x99, 0x09, 0x22, 0x01, 0x01, 0x18, 0x00, 0xE2, 0x01, 0x01, 0x18, 0x00, 0xC0, 0x01, 0x01, 0x1F, 0xFF, 0x20, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x38, 0x00, 0xE2, 0x01, 0x01, 0x40, 0x00, 0xF2, 0x01, 0x00, 0xE9, 0x08, 0x20, 0x01, 0x00, 0xEA, 0x49, 0x22, 0x41, 0x01, 0x42, 0x49, 0x22, 0x01, 0x00, 0xD9, 0x08, 0x34, 0x01, 0x00, 0xDA, 0x49, 0x22, 0x41, 0x01, 0x3A, 0x49, 0x82, 0x01, 0x01, 0x3A, 0x49, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0xE0, 0x00, 0xE2, 0x01, 0x00, 0xF0, 0x00, 0xF2, 0x01, 0x01, 0x48, 0x00, 0xF6, 0x01, 0x01, 0x09, 0x08, 0x20, 0x09, 0x01, 0x10, 0x00, 0xE2, 0x01, 0x00, 0xF9, 0x08, 0x20, 0x01, 0xFF, 0xF2, 0x4B, 0x22, 0x67, 0x01, 0x00, 0x00, 0xE2, 0x01, 0x01, 0x49, 0x08, 0x22, 0x49, 0x01, 0x49, 0x08, 0x20, 0x01, 0x01, 0x00, 0x00, 0xE2, 0x27, 0xFF, 0xF2, 0x4A, 0x20, 0x01, 0x01, 0x10, 0x00, 0xE2, 0x27, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x11, 0x08, 0x20, 0x09, 0xFF, 0xF9, 0x08, 0x22, 0x41, 0x01, 0x10, 0x00, 0xE2, 0x26, 0x01, 0x01, 0x19, 0x20, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x50, 0x00, 0xE2, 0x01, 0x01, 0x58, 0x00, 0xF2, 0x01, 0x01, 0x50, 0x00, 0xC0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x02, 0x07, 0xA1, 0x01, 0xFF, 0xE1, 0x08, 0x20, 0x01, 0x00, 0x02, 0x06, 0xA1, 0x01, 0xFF, 0xE1, 0x08, 0x22, 0x41, 0x01, 0x20, 0x00, 0xE2, 0x01, 0x01, 0x58, 0x00, 0xC0, 0x01, 0x01, 0x27, 0xFF, 0x20, 0x01, 0xFF, 0xE1, 0x08, 0x22, 0x01, 0x01, 0x30, 0x00, 0xE2, 0x01, 0x01, 0x30, 0x00, 0xC0, 0x01, 0x00, 0x97, 0xFF, 0x20, 0x01, 0x00, 0xC8, 0x00, 0xE2, 0x01, 0x00, 0x9F, 0xFF, 0x20, 0x01, 0x00, 0xD0, 0x00, 0xE2, 0x01, 0x00, 0xC9, 0x08, 0x20, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x68, 0x00, 0xE2, 0x01, 0x01, 0x68, 0x00, 0xE2, 0x01, 0x01, 0x68, 0x00, 0xE2, 0x01, 0x01, 0x68, 0x00, 0xE2, 0x01, 0x01, 0x68, 0x00, 0xE2, 0x01, 0x01, 0x68, 0x00, 0xE2, 0x01, 0x01, 0x8A, 0x4E, 0x20, 0x01, 0x01, 0x7A, 0x4E, 0x34, 0x01, 0x01, 0x6A, 0x4C, 0x22, 0x01, 0x01, 0x62, 0x4D, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x80, 0x00, 0xE2, 0x01, 0x01, 0x90, 0x00, 0xF2, 0x01, 0x01, 0xBA, 0x51, 0x20, 0x01, 0x01, 0xB2, 0x53, 0x22, 0x01, 0x01, 0xA2, 0x51, 0x34, 0x01, 0x01, 0x9A, 0x53, 0x22, 0x01, 0x01, 0x82, 0x4F, 0x22, 0x01, 0x01, 0x7A, 0x50, 0x22, 0x01, 0x01, 0x72, 0x52, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0xA8, 0x00, 0xE2, 0x01, 0x01, 0xC0, 0x00, 0xF2, 0x01, 0x01, 0xEA, 0x56, 0x20, 0x01, 0x01, 0xE2, 0x58, 0x22, 0x01, 0x01, 0xD2, 0x56, 0x34, 0x01, 0x01, 0xCA, 0x58, 0x22, 0x01, 0x01, 0xAA, 0x54, 0x22, 0x01, 0x01, 0xA2, 0x55, 0x22, 0x01, 0x01, 0x9A, 0x57, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0xD8, 0x00, 0xE2, 0x01, 0x01, 0xF0, 0x00, 0xF2, 0x01, 0x02, 0x1A, 0x5B, 0x20, 0x01, 0x02, 0x12, 0x5D, 0x22, 0x01, 0x02, 0x02, 0x5B, 0x34, 0x01, 0x01, 0xFA, 0x5D, 0x22, 0x01, 0x01, 0xDA, 0x59, 0x22, 0x01, 0x01, 0xD2, 0x5A, 0x22, 0x01, 0x01, 0xCA, 0x5C, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x08, 0x00, 0xE2, 0x01, 0x02, 0x20, 0x00, 0xF2, 0x01, 0x02, 0x4A, 0x60, 0x20, 0x01, 0x02, 0x42, 0x62, 0x22, 0x01, 0x02, 0x32, 0x60, 0x34, 0x01, 0x02, 0x2A, 0x62, 0x22, 0x01, 0x02, 0x0A, 0x5E, 0x22, 0x01, 0x02, 0x02, 0x5F, 0x22, 0x01, 0x01, 0xFA, 0x61, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x38, 0x00, 0xE2, 0x01, 0x02, 0x50, 0x00, 0xF2, 0x01, 0x02, 0x7A, 0x65, 0x20, 0x01, 0x02, 0x72, 0x67, 0x22, 0x01, 0x02, 0x62, 0x65, 0x34, 0x01, 0x02, 0x5A, 0x67, 0x22, 0x01, 0x02, 0x3A, 0x63, 0x22, 0x01, 0x02, 0x32, 0x64, 0x22, 0x01, 0x02, 0x2A, 0x66, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x68, 0x00, 0xE2, 0x01, 0x02, 0x80, 0x00, 0xF2, 0x01, 0x02, 0xAA, 0x6A, 0x20, 0x01, 0x02, 0xA2, 0x6C, 0x22, 0x01, 0x02, 0x92, 0x6A, 0x34, 0x01, 0x02, 0x8A, 0x6C, 0x22, 0x01, 0x02, 0x6A, 0x68, 0x22, 0x01, 0x02, 0x62, 0x69, 0x22, 0x01, 0x02, 0x5A, 0x6B, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x98, 0x00, 0xE2, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x04, 0x28, 0x00, 0xE2, 0x01, 0x02, 0xB0, 0x00, 0xF2, 0x01, 0x00, 0xD1, 0x08, 0x20, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0xC0, 0x00, 0xE2, 0x01, 0x02, 0xC0, 0x00, 0xE2, 0x01, 0x02, 0xC0, 0x00, 0xE2, 0x01, 0x02, 0xC0, 0x00, 0xE2, 0x01, 0x02, 0xC0, 0x00, 0xE2, 0x01, 0x02, 0xC0, 0x00, 0xE2, 0x01, 0x02, 0xE2, 0x6F, 0x20, 0x01, 0x02, 0xD2, 0x6F, 0x34, 0x01, 0x02, 0xC2, 0x6D, 0x22, 0x01, 0x02, 0xBA, 0x6E, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0xD8, 0x00, 0xE2, 0x01, 0x02, 0xE8, 0x00, 0xF2, 0x01, 0x03, 0x12, 0x72, 0x20, 0x01, 0x03, 0x0A, 0x74, 0x22, 0x01, 0x02, 0xFA, 0x72, 0x34, 0x01, 0x02, 0xF2, 0x74, 0x22, 0x01, 0x02, 0xDA, 0x70, 0x22, 0x01, 0x02, 0xD2, 0x71, 0x22, 0x01, 0x02, 0xCA, 0x73, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x03, 0x00, 0x00, 0xE2, 0x01, 0x03, 0x18, 0x00, 0xF2, 0x01, 0x03, 0x42, 0x77, 0x20, 0x01, 0x03, 0x3A, 0x79, 0x22, 0x01, 0x03, 0x2A, 0x77, 0x34, 0x01, 0x03, 0x22, 0x79, 0x22, 0x01, 0x03, 0x02, 0x75, 0x22, 0x01, 0x02, 0xFA, 0x76, 0x22, 0x01, 0x02, 0xF2, 0x78, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x03, 0x30, 0x00, 0xE2, 0x01, 0x03, 0x48, 0x00, 0xF2, 0x01, 0x03, 0x72, 0x7C, 0x20, 0x01, 0x03, 0x6A, 0x7E, 0x22, 0x01, 0x03, 0x5A, 0x7C, 0x34, 0x01, 0x03, 0x52, 0x7E, 0x22, 0x01, 0x03, 0x32, 0x7A, 0x22, 0x01, 0x03, 0x2A, 0x7B, 0x22, 0x01, 0x03, 0x22, 0x7D, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x03, 0x60, 0x00, 0xE2, 0x01, 0x03, 0x78, 0x00, 0xF2, 0x01, 0x03, 0xA2, 0x81, 0x20, 0x01, 0x03, 0x9A, 0x83, 0x22, 0x01, 0x03, 0x8A, 0x81, 0x34, 0x01, 0x03, 0x82, 0x83, 0x22, 0x01, 0x03, 0x62, 0x7F, 0x22, 0x01, 0x03, 0x5A, 0x80, 0x22, 0x01, 0x03, 0x52, 0x82, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x03, 0x90, 0x00, 0xE2, 0x01, 0x03, 0xA8, 0x00, 0xF2, 0x01, 0x03, 0xD2, 0x86, 0x20, 0x01, 0x03, 0xCA, 0x88, 0x22, 0x01, 0x03, 0xBA, 0x86, 0x34, 0x01, 0x03, 0xB2, 0x88, 0x22, 0x01, 0x03, 0x92, 0x84, 0x22, 0x01, 0x03, 0x8A, 0x85, 0x22, 0x01, 0x03, 0x82, 0x87, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x03, 0xC0, 0x00, 0xE2, 0x01, 0x03, 0xD8, 0x00, 0xF2, 0x01, 0x04, 0x02, 0x8B, 0x20, 0x01, 0x03, 0xFA, 0x8D, 0x22, 0x01, 0x03, 0xEA, 0x8B, 0x34, 0x01, 0x03, 0xE2, 0x8D, 0x22, 0x01, 0x03, 0xC2, 0x89, 0x22, 0x01, 0x03, 0xBA, 0x8A, 0x22, 0x01, 0x03, 0xB2, 0x8C, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x03, 0xF0, 0x00, 0xE2, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x04, 0x58, 0x00, 0xE2, 0x01, 0x04, 0x08, 0x00, 0xF2, 0x01, 0x00, 0x3A, 0x8E, 0x20, 0x01, 0x04, 0x10, 0x00, 0xE2, 0x01, 0x04, 0x10, 0x00, 0xC0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x04, 0x82, 0x92, 0x21, 0x01, 0x04, 0x7A, 0x93, 0x23, 0x01, 0x04, 0x3A, 0x92, 0x35, 0x01, 0x04, 0x32, 0x93, 0x23, 0x01, 0x04, 0x2A, 0x8F, 0x23, 0x01, 0x04, 0x22, 0x90, 0x23, 0x01, 0x04, 0x1A, 0x91, 0x23, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x04, 0x40, 0x00, 0xE2, 0x01, 0x04, 0x88, 0x00, 0xF2, 0x01, 0x04, 0x9A, 0x92, 0x21, 0x01, 0x04, 0x92, 0x93, 0x23, 0x01, 0x04, 0x6A, 0x92, 0x35, 0x01, 0x04, 0x62, 0x93, 0x23, 0x01, 0x04, 0x5A, 0x8F, 0x23, 0x01, 0x04, 0x52, 0x90, 0x23, 0x01, 0x04, 0x4A, 0x91, 0x23, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x04, 0x70, 0x00, 0xE2, 0x01, 0x04, 0xA0, 0x00, 0xF2, 0x01, 0x04, 0x42, 0xA3, 0x20, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x04, 0xC8, 0x00, 0xE2, 0x01, 0x04, 0xF2, 0xA6, 0x20, 0x01, 0x04, 0xEA, 0xA8, 0x22, 0x01, 0x04, 0xDA, 0xA6, 0x34, 0x01, 0x04, 0xD2, 0xA8, 0x22, 0x01, 0x04, 0xCA, 0xA4, 0x22, 0x01, 0x04, 0xC2, 0xA5, 0x22, 0x01, 0x04, 0xBA, 0xA7, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x04, 0xE0, 0x00, 0xE2, 0x01, 0x04, 0xF8, 0x00, 0xF2, 0x01, 0x05, 0x22, 0xAB, 0x20, 0x01, 0x05, 0x1A, 0xAD, 0x22, 0x01, 0x05, 0x0A, 0xAB, 0x34, 0x01, 0x05, 0x02, 0xAD, 0x22, 0x01, 0x04, 0xE2, 0xA9, 0x22, 0x01, 0x04, 0xDA, 0xAA, 0x22, 0x01, 0x04, 0xD2, 0xAC, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x05, 0x10, 0x00, 0xE2, 0x01, 0x04, 0xA8, 0x00, 0xE2, 0x01, 0x05, 0x28, 0x00, 0xF2, 0x01, 0x04, 0x41, 0x08, 0x20, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x05, 0x40, 0x00, 0xE2, 0x01, 0x05, 0x6A, 0xB0, 0x20, 0x01, 0x05, 0x62, 0xB2, 0x22, 0x01, 0x05, 0x52, 0xB0, 0x34, 0x01, 0x05, 0x4A, 0xB2, 0x22, 0x01, 0x05, 0x42, 0xAE, 0x22, 0x01, 0x05, 0x3A, 0xAF, 0x22, 0x01, 0x05, 0x32, 0xB1, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x05, 0x58, 0x00, 0xE2, 0x01, 0x05, 0x70, 0x00, 0xF2, 0x01, 0x05, 0x9A, 0xB5, 0x20, 0x01, 0x05, 0x92, 0xB7, 0x22, 0x01, 0x05, 0x82, 0xB5, 0x34, 0x01, 0x05, 0x7A, 0xB7, 0x22, 0x01, 0x05, 0x5A, 0xB3, 0x22, 0x01, 0x05, 0x52, 0xB4, 0x22, 0x01, 0x05, 0x4A, 0xB6, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x05, 0x88, 0x00, 0xE2, 0x01, 0x04, 0xB0, 0x00, 0xE2, 0x01, 0x05, 0xA0, 0x00, 0xF2, 0x01, 0x04, 0x72, 0xB8, 0x20, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x05, 0xC8, 0x00, 0xE2, 0x01, 0x05, 0xF2, 0xBB, 0x20, 0x01, 0x05, 0xEA, 0xBD, 0x22, 0x01, 0x05, 0xDA, 0xBB, 0x34, 0x01, 0x05, 0xD2, 0xBD, 0x22, 0x01, 0x05, 0xCA, 0xB9, 0x22, 0x01, 0x05, 0xC2, 0xBA, 0x22, 0x01, 0x05, 0xBA, 0xBC, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x05, 0xE0, 0x00, 0xE2, 0x01, 0x05, 0xF8, 0x00, 0xF2, 0x01, 0x06, 0x22, 0xC0, 0x20, 0x01, 0x06, 0x1A, 0xC2, 0x22, 0x01, 0x06, 0x0A, 0xC0, 0x34, 0x01, 0x06, 0x02, 0xC2, 0x22, 0x01, 0x05, 0xE2, 0xBE, 0x22, 0x01, 0x05, 0xDA, 0xBF, 0x22, 0x01, 0x05, 0xD2, 0xC1, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x06, 0x10, 0x00, 0xE2, 0x01, 0x05, 0xA8, 0x00, 0xE2, 0x01, 0x06, 0x28, 0x00, 0xF2, 0x01, 0x04, 0x71, 0x08, 0x20, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x06, 0x40, 0x00, 0xE2, 0x01, 0x06, 0x6A, 0xC5, 0x20, 0x01, 0x06, 0x62, 0xC7, 0x22, 0x01, 0x06, 0x52, 0xC5, 0x34, 0x01, 0x06, 0x4A, 0xC7, 0x22, 0x01, 0x06, 0x42, 0xC3, 0x22, 0x01, 0x06, 0x3A, 0xC4, 0x22, 0x01, 0x06, 0x32, 0xC6, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x06, 0x58, 0x00, 0xE2, 0x01, 0x06, 0x70, 0x00, 0xF2, 0x01, 0x06, 0x9A, 0xCA, 0x20, 0x01, 0x06, 0x92, 0xCC, 0x22, 0x01, 0x06, 0x82, 0xCA, 0x34, 0x01, 0x06, 0x7A, 0xCC, 0x22, 0x01, 0x06, 0x5A, 0xC8, 0x22, 0x01, 0x06, 0x52, 0xC9, 0x22, 0x01, 0x06, 0x4A, 0xCB, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x06, 0x88, 0x00, 0xE2, 0x01, 0x05, 0xB0, 0x00, 0xE2, 0x01, 0x06, 0xA0, 0x00, 0xF2, 0x01, 0x00, 0x21, 0x08, 0x20, 0x01, 0x06, 0xB9, 0x08, 0x22, 0x41, 0x06, 0xD8, 0x00, 0xE2, 0x01, 0x06, 0xC9, 0x08, 0x20, 0x01, 0x06, 0xB9, 0x08, 0x34, 0x01, 0x06, 0xDA, 0xCD, 0x22, 0x01, 0x06, 0xC0, 0x00, 0xE2, 0x01, 0x06, 0xC0, 0x00, 0xC0, 0x01, 0x06, 0xD0, 0x00, 0xF2, 0x01, 0x04, 0xB7, 0xFF, 0x20, 0x01, 0x06, 0xA8, 0x00, 0xE2, 0x01, 0x05, 0xB7, 0xFF, 0x20, 0x01, 0x06, 0xB0, 0x00, 0xE2, 0x01, 0x00, 0x19, 0x08, 0x20, 0x01, 0x06, 0xF1, 0x08, 0x22, 0x41, 0x07, 0x10, 0x00, 0xE2, 0x01, 0x07, 0x01, 0x08, 0x20, 0x01, 0x06, 0xF1, 0x08, 0x34, 0x01, 0x07, 0x12, 0xCE, 0x22, 0x01, 0x06, 0xF8, 0x00, 0xE2, 0x01, 0x06, 0xF8, 0x00, 0xC0, 0x01, 0x07, 0x08, 0x00, 0xF2, 0x01, 0x04, 0xAF, 0xFF, 0x20, 0x01, 0x06, 0xE0, 0x00, 0xE2, 0x01, 0x05, 0xAF, 0xFF, 0x20, 0x01, 0x06, 0xE8, 0x00, 0xE2, 0x01, 0xFF, 0xF2, 0xCF, 0x20, 0x01, 0x07, 0x19, 0x08, 0x22, 0x41, 0x07, 0x58, 0x00, 0xE2, 0x01, 0x07, 0x29, 0x08, 0x20, 0x01, 0x07, 0x19, 0x08, 0x34, 0x01, 0x07, 0x59, 0x14, 0x22, 0x01, 0x07, 0x20, 0x00, 0xE2, 0x01, 0x07, 0x20, 0x00, 0xC0, 0x01, 0x07, 0x30, 0x00, 0xF2, 0x01, 0x06, 0xE7, 0xFF, 0x60, 0x01, 0x06, 0xEF, 0xFF, 0x40, 0x01, 0xFF, 0xF2, 0xD0, 0x20, 0x01, 0x07, 0x39, 0x08, 0x22, 0x41, 0x07, 0x58, 0x00, 0xE2, 0x01, 0x07, 0x49, 0x08, 0x20, 0x01, 0x07, 0x39, 0x08, 0x34, 0x01, 0x07, 0x59, 0x14, 0x22, 0x01, 0x07, 0x40, 0x00, 0xE2, 0x01, 0x07, 0x40, 0x00, 0xC0, 0x01, 0x07, 0x50, 0x00, 0xF2, 0x01, 0x06, 0xAF, 0xFF, 0x66, 0x01, 0x06, 0xB7, 0xFF, 0x44, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x07, 0x60, 0x00, 0xE6, 0x01, 0x07, 0x68, 0x00, 0xE4, 0x01, 0x07, 0xA2, 0xD1, 0x20, 0x01, 0x07, 0x91, 0x08, 0x22, 0x01, 0x07, 0x98, 0x00, 0xE2, 0x01, 0x07, 0x99, 0x08, 0x20, 0x41, 0x07, 0x61, 0x08, 0x22, 0x01, 0x07, 0x70, 0x00, 0xC0, 0x01, 0x07, 0xA7, 0xFF, 0x22, 0x41, 0x07, 0x80, 0x00, 0xE2, 0x01, 0x07, 0x82, 0xD1, 0x20, 0x01, 0x07, 0xA1, 0x08, 0x22, 0x01, 0x07, 0xA8, 0x00, 0xE2, 0x01, 0x07, 0xC2, 0xD1, 0x20, 0x01, 0x07, 0xB1, 0x08, 0x22, 0x01, 0x07, 0xB8, 0x00, 0xE2, 0x01, 0x07, 0xB9, 0x08, 0x20, 0x41, 0x07, 0x69, 0x08, 0x22, 0x01, 0x07, 0x70, 0x00, 0xC0, 0x01, 0x07, 0xC7, 0xFF, 0x22, 0x41, 0x07, 0x88, 0x00, 0xE2, 0x01, 0x07, 0x8A, 0xD1, 0x20, 0x01, 0x07, 0xC1, 0x08, 0x22, 0x01, 0x07, 0xC8, 0x00, 0xE2, 0x01, 0x07, 0xA9, 0x08, 0x22, 0x01, 0x07, 0xE0, 0x00, 0xE2, 0x01, 0x07, 0xF2, 0xD5, 0x20, 0x01, 0x07, 0xEA, 0xD6, 0x22, 0x01, 0x07, 0xE2, 0xD2, 0x22, 0x01, 0x07, 0xDA, 0xD3, 0x22, 0x01, 0x07, 0xD2, 0xD4, 0x22, 0x01, 0x07, 0xF8, 0x00, 0xE2, 0x01, 0x07, 0xF8, 0x00, 0xC0, 0x01, 0x07, 0xFF, 0xFF, 0x20, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x08, 0x30, 0x00, 0xE2, 0x01, 0x08, 0x38, 0x00, 0xF2, 0x01, 0x08, 0x11, 0x08, 0x20, 0x01, 0x08, 0x12, 0xF8, 0x22, 0x41, 0x08, 0x3A, 0xF8, 0x22, 0x01, 0x08, 0x01, 0x08, 0x34, 0x01, 0x08, 0x02, 0xF8, 0x22, 0x41, 0x08, 0x32, 0xF8, 0x82, 0x01, 0x08, 0x32, 0xF8, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x08, 0x08, 0x00, 0xE2, 0x01, 0x08, 0x18, 0x00, 0xF2, 0x01, 0x08, 0x40, 0x00, 0xF6, 0x01, 0x08, 0x41, 0x1A, 0x20, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x08, 0x48, 0x00, 0xE2, 0x01, 0x08, 0x50, 0x00, 0xF2, 0x01, 0x08, 0x48, 0x00, 0xC0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x02, 0xD8, 0xA1, 0x01, 0xFF, 0xE1, 0x08, 0x20, 0x01, 0x00, 0x02, 0xD7, 0xA1, 0x01, 0xFF, 0xE1, 0x08, 0x22, 0x41, 0x08, 0x20, 0x00, 0xE2, 0x01, 0x08, 0x50, 0x00, 0xC0, 0x01, 0x08, 0x27, 0xFF, 0x20, 0x01, 0xFF, 0xE1, 0x08, 0x22, 0x01, 0x07, 0x78, 0x00, 0xE2, 0x01, 0x07, 0x82, 0xF9, 0x20, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x08, 0x78, 0x00, 0xE2, 0x01, 0x08, 0xA2, 0xFC, 0x20, 0x01, 0x08, 0x9A, 0xFE, 0x22, 0x01, 0x08, 0x8A, 0xFC, 0x34, 0x01, 0x08, 0x82, 0xFE, 0x22, 0x01, 0x08, 0x7A, 0xFA, 0x22, 0x01, 0x08, 0x72, 0xFB, 0x22, 0x01, 0x08, 0x6A, 0xFD, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x08, 0x90, 0x00, 0xE2, 0x01, 0x08, 0xA8, 0x00, 0xF2, 0x01, 0x08, 0xD3, 0x01, 0x20, 0x01, 0x08, 0xCB, 0x03, 0x22, 0x01, 0x08, 0xBB, 0x01, 0x34, 0x01, 0x08, 0xB3, 0x03, 0x22, 0x01, 0x08, 0x92, 0xFF, 0x22, 0x01, 0x08, 0x8B, 0x00, 0x22, 0x01, 0x08, 0x83, 0x02, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x08, 0xC0, 0x00, 0xE2, 0x01, 0x08, 0x58, 0x00, 0xE2, 0x01, 0x08, 0xD8, 0x00, 0xF2, 0x01, 0x07, 0x81, 0x08, 0x20, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x08, 0xF0, 0x00, 0xE2, 0x01, 0x09, 0x1B, 0x06, 0x20, 0x01, 0x09, 0x13, 0x08, 0x22, 0x01, 0x09, 0x03, 0x06, 0x34, 0x01, 0x08, 0xFB, 0x08, 0x22, 0x01, 0x08, 0xF3, 0x04, 0x22, 0x01, 0x08, 0xEB, 0x05, 0x22, 0x01, 0x08, 0xE3, 0x07, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x09, 0x08, 0x00, 0xE2, 0x01, 0x09, 0x20, 0x00, 0xF2, 0x01, 0x09, 0x4B, 0x0B, 0x20, 0x01, 0x09, 0x43, 0x0D, 0x22, 0x01, 0x09, 0x33, 0x0B, 0x34, 0x01, 0x09, 0x2B, 0x0D, 0x22, 0x01, 0x09, 0x0B, 0x09, 0x22, 0x01, 0x09, 0x03, 0x0A, 0x22, 0x01, 0x08, 0xFB, 0x0C, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x09, 0x38, 0x00, 0xE2, 0x01, 0x08, 0x60, 0x00, 0xE2, 0x01, 0x09, 0x50, 0x00, 0xF2, 0x01, 0x07, 0x8B, 0x0E, 0x20, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x09, 0x78, 0x00, 0xE2, 0x01, 0x09, 0xA3, 0x11, 0x20, 0x01, 0x09, 0x9B, 0x13, 0x22, 0x01, 0x09, 0x8B, 0x11, 0x34, 0x01, 0x09, 0x83, 0x13, 0x22, 0x01, 0x09, 0x7B, 0x0F, 0x22, 0x01, 0x09, 0x73, 0x10, 0x22, 0x01, 0x09, 0x6B, 0x12, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x09, 0x90, 0x00, 0xE2, 0x01, 0x09, 0xA8, 0x00, 0xF2, 0x01, 0x09, 0xD3, 0x16, 0x20, 0x01, 0x09, 0xCB, 0x18, 0x22, 0x01, 0x09, 0xBB, 0x16, 0x34, 0x01, 0x09, 0xB3, 0x18, 0x22, 0x01, 0x09, 0x93, 0x14, 0x22, 0x01, 0x09, 0x8B, 0x15, 0x22, 0x01, 0x09, 0x83, 0x17, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x09, 0xC0, 0x00, 0xE2, 0x01, 0x09, 0x58, 0x00, 0xE2, 0x01, 0x09, 0xD8, 0x00, 0xF2, 0x01, 0x07, 0x89, 0x08, 0x20, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x09, 0xF0, 0x00, 0xE2, 0x01, 0x0A, 0x1B, 0x1B, 0x20, 0x01, 0x0A, 0x13, 0x1D, 0x22, 0x01, 0x0A, 0x03, 0x1B, 0x34, 0x01, 0x09, 0xFB, 0x1D, 0x22, 0x01, 0x09, 0xF3, 0x19, 0x22, 0x01, 0x09, 0xEB, 0x1A, 0x22, 0x01, 0x09, 0xE3, 0x1C, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x0A, 0x08, 0x00, 0xE2, 0x01, 0x0A, 0x20, 0x00, 0xF2, 0x01, 0x0A, 0x4B, 0x20, 0x20, 0x01, 0x0A, 0x43, 0x22, 0x22, 0x01, 0x0A, 0x33, 0x20, 0x34, 0x01, 0x0A, 0x2B, 0x22, 0x22, 0x01, 0x0A, 0x0B, 0x1E, 0x22, 0x01, 0x0A, 0x03, 0x1F, 0x22, 0x01, 0x09, 0xFB, 0x21, 0x22, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x0A, 0x38, 0x00, 0xE2, 0x01, 0x09, 0x60, 0x00, 0xE2, 0x01, 0x0A, 0x50, 0x00, 0xF2, 0x01, 0x08, 0x5B, 0x23, 0x20, 0x01, 0x0A, 0x58, 0x00, 0xE2, 0x01, 0x08, 0x63, 0x24, 0x20, 0x01, 0x0A, 0x60, 0x00, 0xE2, 0x01, 0x09, 0x5B, 0x25, 0x20, 0x01, 0x0A, 0x68, 0x00, 0xE2, 0x01, 0x09, 0x63, 0x26, 0x20, 0x01, 0x0A, 0x70, 0x00, 0xE2, 0x01, 0x0A, 0x59, 0x08, 0x20, 0x01, 0xFF, 0x68, 0x00, 0x02, 0x01, 0x0A, 0x61, 0x08, 0x20, 0x01, 0xFF, 0x70, 0x00, 0x02, 0x01, 0x0A, 0x69, 0x08, 0x20, 0x01, 0xFF, 0x78, 0x00, 0x02, 0x01, 0x0A, 0x71, 0x08, 0x20, 0x01, 0xFF, 0x80, 0x00, 0x02, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, </Data>
        </Program>
        <Register>
            <Name>Param</Name>
            <Address>0</Address>
            <AddrIncr>4</AddrIncr>
            <Size>4096</Size>
            <Data>0x00, 0x00, 0x00, 0x03, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0xF3, 0xE6, 0x23, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x13, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x12, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x76, 0x09, 0x11, 0x0F, 0x38, 0x40, 0xAB, 0x00, 0xC7, 0xBF, 0x55, 0x00, 0x64, 0x41, 0x17, 0x0F, 0xA5, 0xB5, 0xD7, 0x00, 0xA6, 0x09, 0x7A, 0x00, 0x37, 0x05, 0xAC, 0x0F, 0xC8, 0xFA, 0x54, 0x00, 0x16, 0x16, 0x9C, 0x0F, 0xC3, 0xDF, 0xEA, 0x00, 0x7D, 0xB7, 0x3E, 0x0F, 0x07, 0xB3, 0xD5, 0x00, 0xF8, 0x4C, 0x2B, 0x00, 0x7B, 0x6A, 0x2E, 0x0F, 0x86, 0xDE, 0x94, 0x00, 0x80, 0x67, 0x16, 0x0F, 0x04, 0x1F, 0xCA, 0x00, 0xFB, 0xE0, 0x36, 0x00, 0x7B, 0x8F, 0x79, 0x0F, 0x84, 0x09, 0x71, 0x00, 0x80, 0x25, 0x65, 0x0F, 0x01, 0x26, 0xC6, 0x00, 0xFE, 0xD9, 0x3A, 0x00, 0x7E, 0xB6, 0x7E, 0x0F, 0x81, 0x24, 0x1D, 0x00, 0x7B, 0x2D, 0xD7, 0x0F, 0x73, 0x39, 0xB8, 0x00, 0x8C, 0xC6, 0x48, 0x00, 0x66, 0xD6, 0x84, 0x0F, 0x9D, 0xFB, 0xA4, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x04, 0xB7, 0x0F, 0x37, 0x01, 0x5B, 0x00, 0xC8, 0xFE, 0xA5, 0x00, 0x64, 0x4A, 0xEE, 0x0F, 0xA3, 0xB0, 0x5B, 0x00, 0x93, 0x9B, 0xD9, 0x00, 0x2C, 0x94, 0x9D, 0x0F, 0xD3, 0x6B, 0x63, 0x00, 0x1E, 0xFA, 0xA4, 0x0F, 0xCD, 0x69, 0x83, 0x00, 0x7E, 0x70, 0x7C, 0x0F, 0x06, 0xC4, 0xFE, 0x00, 0xF9, 0x3B, 0x02, 0x00, 0x7B, 0xA7, 0x93, 0x0F, 0x85, 0xE7, 0xF1, 0x00, 0x80, 0x67, 0x16, 0x0F, 0x04, 0x1F, 0xCA, 0x00, 0xFB, 0xE0, 0x36, 0x00, 0x7B, 0x8F, 0x79, 0x0F, 0x84, 0x09, 0x71, 0x00, 0x80, 0x25, 0x65, 0x0F, 0x01, 0x26, 0xC6, 0x00, 0xFE, 0xD9, 0x3A, 0x00, 0x7E, 0xB6, 0x7E, 0x0F, 0x81, 0x24, 0x1D, 0x00, 0x7C, 0x99, 0xEF, 0x0F, 0x70, 0xEF, 0xD1, 0x00, 0x8F, 0x10, 0x2F, 0x00, 0x67, 0x09, 0xA3, 0x0F, 0x9C, 0x5C, 0x6E, 0x02, 0x80, 0x00, 0x00, 0x00, 0x47, 0x51, 0x07, 0xFF, 0x71, 0x5D, 0xF1, 0x00, 0x47, 0x51, 0x07, 0x00, 0xFD, 0xA1, 0x6A, 0xFF, 0x82, 0x59, 0x05, 0x00, 0x47, 0x7D, 0x8F, 0xFF, 0x71, 0x04, 0xE2, 0x00, 0x47, 0x7D, 0x8F, 0x00, 0xFE, 0x41, 0x0F, 0xFF, 0x81, 0xBB, 0xEA, 0x00, 0x47, 0x9E, 0x70, 0xFF, 0x70, 0xC3, 0x1F, 0x00, 0x47, 0x9E, 0x70, 0x00, 0xFE, 0xB6, 0xB0, 0xFF, 0x81, 0x47, 0xAA, 0x00, 0x47, 0xB6, 0xB4, 0xFF, 0x70, 0x92, 0x98, 0x00, 0x47, 0xB6, 0xB4, 0x00, 0xFF, 0x0D, 0x5C, 0xFF, 0x80, 0xF1, 0xBF, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x00, 0x00, 0x04, 0xB0, 0x00, 0xFC, 0xEB, 0x75, 0x00, 0x00, 0x02, 0x58, 0x0F, 0x83, 0x0B, 0x2B, 0x00, 0x00, 0x02, 0x58, 0x00, 0x00, 0x04, 0xB0, 0x00, 0xFC, 0xEB, 0x75, 0x00, 0x00, 0x02, 0x58, 0x0F, 0x83, 0x0B, 0x2B, 0x00, 0x7E, 0x78, 0x12, 0x0F, 0x03, 0x0F, 0xDB, 0x00, 0xFC, 0xEB, 0x75, 0x00, 0x7E, 0x78, 0x12, 0x0F, 0x83, 0x0B, 0x2B, 0x00, 0x7E, 0x78, 0x12, 0x0F, 0x03, 0x0F, 0xDB, 0x00, 0xFC, 0xEB, 0x75, 0x00, 0x7E, 0x78, 0x12, 0x0F, 0x83, 0x0B, 0x2B, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x00, 0x00, 0x04, 0xB0, 0x00, 0xFC, 0xEB, 0x75, 0x00, 0x00, 0x02, 0x58, 0x0F, 0x83, 0x0B, 0x2B, 0x00, 0x00, 0x02, 0x58, 0x00, 0x00, 0x04, 0xB0, 0x00, 0xFC, 0xEB, 0x75, 0x00, 0x00, 0x02, 0x58, 0x0F, 0x83, 0x0B, 0x2B, 0x00, 0x7E, 0x78, 0x12, 0x0F, 0x03, 0x0F, 0xDB, 0x00, 0xFC, 0xEB, 0x75, 0x00, 0x7E, 0x78, 0x12, 0x0F, 0x83, 0x0B, 0x2B, 0x00, 0x7E, 0x78, 0x12, 0x0F, 0x03, 0x0F, 0xDB, 0x00, 0xFC, 0xEB, 0x75, 0x00, 0x7E, 0x78, 0x12, 0x0F, 0x83, 0x0B, 0x2B, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x01, 0x01, 0x5C, 0x00, 0x00, 0x03, 0x1D, 0x00, 0x00, 0x06, 0x3B, 0x00, 0x00, 0x03, 0x1D, 0x00, 0xFC, 0x72, 0x05, 0x0F, 0x83, 0x81, 0x85, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x50, 0x05, 0xA9, 0x00, 0x9F, 0xAA, 0x43, 0x00, 0xE3, 0x9E, 0xA9, 0x00, 0xE3, 0x9E, 0xA9, 0x00, 0xE3, 0x9E, 0xA9, 0x00, 0xE3, 0x9E, 0xA9, 0x00, 0xE3, 0x9E, 0xA9, 0x00, 0xE3, 0x9E, 0xA9, 0x00, 0xE3, 0x9E, 0xA9, 0x00, 0x00, 0x06, 0xD3, 0x00, 0x80, 0x00, 0x00, 0x00, 0x01, 0x12, 0x6D, 0x00, 0x02, 0x24, 0xD9, 0x00, 0xEA, 0x79, 0x3E, 0x00, 0x01, 0x12, 0x6D, 0x0F, 0x91, 0x3D, 0x0F, 0x00, 0x00, 0xFA, 0x90, 0x00, 0x01, 0xF5, 0x21, 0x00, 0xD6, 0x16, 0x0D, 0x00, 0x00, 0xFA, 0x90, 0x0F, 0xA5, 0xFF, 0xB2, 0x00, 0x73, 0xE1, 0x27, 0x0F, 0x18, 0x3D, 0xB2, 0x00, 0xE4, 0x83, 0x2C, 0x00, 0x73, 0xE1, 0x27, 0x0F, 0x94, 0xFE, 0x90, 0x00, 0x67, 0xD6, 0x2F, 0x0F, 0x30, 0x53, 0xA1, 0x00, 0xCC, 0xC3, 0x9D, 0x00, 0x67, 0xD6, 0x2F, 0x0F, 0xAD, 0x6A, 0xDF, 0x00, 0x80, 0x00, 0x00, 0x00, 0x02, 0x31, 0xDC, 0x00, 0x04, 0x63, 0xB8, 0x00, 0xCC, 0x68, 0x2F, 0x00, 0x02, 0x31, 0xDC, 0x0F, 0xAA, 0xD0, 0x61, 0x00, 0x02, 0x31, 0xDC, 0x00, 0x04, 0x63, 0xB8, 0x00, 0xCC, 0x68, 0x2F, 0x00, 0x02, 0x31, 0xDC, 0x0F, 0xAA, 0xD0, 0x61, 0x00, 0x68, 0x65, 0xF4, 0x0F, 0x2F, 0x34, 0x19, 0x00, 0xCC, 0x68, 0x2F, 0x00, 0x68, 0x65, 0xF4, 0x0F, 0xAA, 0xD0, 0x61, 0x00, 0x68, 0x65, 0xF4, 0x0F, 0x2F, 0x34, 0x19, 0x00, 0xCC, 0x68, 0x2F, 0x00, 0x68, 0x65, 0xF4, 0x0F, 0xAA, 0xD0, 0x61, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, </Data>
        </Register>
        <Register>
            <Name>IC 1.HWConFiguration</Name>
            <Address>2076</Address>
            <AddrIncr>0</AddrIncr>
            <Size>24</Size>
            <Data>0x00, 0x18, 0x08, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, </Data>
        </Register>
        <Register>
            <Name>IC 1.CoreRegister</Name>
            <Address>2076</Address>
            <AddrIncr>0</AddrIncr>
            <Size>2</Size>
            <Data>0x00, 0x1C, </Data>
        </Register>
        <Module>
            <CellName>Mute1</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>MuteNoSlewAlg1</DetailedName>
                <Description>No Slew (Standard)( 1 ) : Mute[ False ];</Description>
                <ModuleParameter>
                    <Name>MuteNoSlewAlg1mute</Name>
                    <Type>FixedPoint</Type>
                    <Address>291</Address>
                    <Value>1</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>Mute2</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>MuteNoSlewAlg2</DetailedName>
                <Description>No Slew (Standard)( 1 ) : Mute[ False ];</Description>
                <ModuleParameter>
                    <Name>MuteNoSlewAlg2mute</Name>
                    <Type>FixedPoint</Type>
                    <Address>292</Address>
                    <Value>1</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>Mute3</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>MuteNoSlewAlg3</DetailedName>
                <Description>No Slew (Standard)( 1 ) : Mute[ False ];</Description>
                <ModuleParameter>
                    <Name>MuteNoSlewAlg3mute</Name>
                    <Type>FixedPoint</Type>
                    <Address>293</Address>
                    <Value>1</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>Mute4</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>MuteNoSlewAlg4</DetailedName>
                <Description>No Slew (Standard)( 1 ) : Mute[ False ];</Description>
                <ModuleParameter>
                    <Name>MuteNoSlewAlg4mute</Name>
                    <Type>FixedPoint</Type>
                    <Address>294</Address>
                    <Value>1</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>Bass Boost1</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>BassBAlg_stereo1</DetailedName>
                <Description>Stereo Dynamic Bass Boost( 1 ) : Frequency[ 60 ],FrequencyL[ 150 ],Boost[ 3 ],Threshold[ -25 ],Gain1[ -5 ],Gain2[ 6.08 ],TimeConstant[ 100 ];</Description>
                <ModuleParameter>
                    <Name>BassBAlg_stereo1freq_varq</Name>
                    <Type>FixedPoint</Type>
                    <Address>209</Address>
                    <Value>0.00785398483276367</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x01, 0x01, 0x5C, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>BassBAlg_stereo1iir_coeff_0_biq</Name>
                    <Type>FixedPoint</Type>
                    <Address>210</Address>
                    <Value>9.50098037719727E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x03, 0x1D, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>BassBAlg_stereo1iir_coeff_1_biq</Name>
                    <Type>FixedPoint</Type>
                    <Address>211</Address>
                    <Value>0.000190138816833496</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x06, 0x3B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>BassBAlg_stereo1iir_coeff_2_biq</Name>
                    <Type>FixedPoint</Type>
                    <Address>212</Address>
                    <Value>9.50098037719727E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x03, 0x1D, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>BassBAlg_stereo1iir_coeff_3_biq</Name>
                    <Type>FixedPoint</Type>
                    <Address>213</Address>
                    <Value>1.9722295999527</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFC, 0x72, 0x05, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>BassBAlg_stereo1iir_coeff_4_biq</Name>
                    <Type>FixedPoint</Type>
                    <Address>214</Address>
                    <Value>-0.972609877586365</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x83, 0x81, 0x85, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>BassBAlg_stereo1compbase_table1_comp</Name>
                    <Type>HexArray</Type>
                    <Address>215</Address>
                    <Size>132</Size>
                    <AddrIncr>4</AddrIncr>
                    <Data>0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x3F, 0x90, 0x56, 0x00, 0x50, 0x05, 0xA9, 0x00, 0x9F, 0xAA, 0x43, 0x00, 0xE3, 0x9E, 0xA9, 0x00, 0xE3, 0x9E, 0xA9, 0x00, 0xE3, 0x9E, 0xA9, 0x00, 0xE3, 0x9E, 0xA9, 0x00, 0xE3, 0x9E, 0xA9, 0x00, 0xE3, 0x9E, 0xA9, 0x00, 0xE3, 0x9E, 0xA9, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>BassBAlg_stereo1TCONST_comp</Name>
                    <Type>FixedPoint</Type>
                    <Address>248</Address>
                    <Value>0.000208258628845215</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x06, 0xD3, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>RMS Limiter Hi Res</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>TwoChannelSingleDetectAlg_Hi_Res1</DetailedName>
                <Description>Stereo Hi Resolution RMS (No Post Gain)( 1 ) : Breakpoints[ X=-90 $ Y=-90 $ -90 $ -90 $ -90 $ -90 $ -90 $ -90 $ -90 $ -90 $ -90 $ -90 $ -90 $ -90 $ 6 $ 6 ],HoldTime[ 0 ],DecayTime[ 10 ],TimeConstant[ 121 ],SoftKnee[ False ],RMS_Label[ RMS TC (dB/s) ],Deacay_Label[ Decay (dB/s) ],DecayTime_ms[ 869 ],TimeConstant_ms[ 72 ],IsdBPS[ True ];</Description>
                <ModuleParameter>
                    <Name>TwoChannelSingleDetectAlg_Hi_Res1</Name>
                    <Type>HexArray</Type>
                    <Address>6</Address>
                    <Size>268</Size>
                    <AddrIncr>4</AddrIncr>
                    <Data>0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>TwoChannelSingleDetectAlg_Hi_Res1RMS</Name>
                    <Type>FixedPoint</Type>
                    <Address>73</Address>
                    <Value>0.000580668449401855</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x13, 0x07, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>TwoChannelSingleDetectAlg_Hi_Res1hold</Name>
                    <Type>FixedPoint</Type>
                    <Address>74</Address>
                    <Value>0</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x00, 0x00, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>TwoChannelSingleDetectAlg_Hi_Res1decay</Name>
                    <Type>FixedPoint</Type>
                    <Address>75</Address>
                    <Value>2.14576721191406E-06</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x00, 0x12, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>SW vol 1_4</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>ExtSWGainDB1</DetailedName>
                <Description>Ext vol (SW slew)( 1 ) : </Description>
                <ModuleParameter>
                    <Name>ExtSWGainDB1step</Name>
                    <Type>FixedPoint</Type>
                    <Address>3</Address>
                    <Value>0.000244140625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x08, 0x00, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>SW vol 1_2</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>ExtSWGainDB3</DetailedName>
                <Description>Ext vol (SW slew)( 1 ) : </Description>
                <ModuleParameter>
                    <Name>ExtSWGainDB3step</Name>
                    <Type>FixedPoint</Type>
                    <Address>206</Address>
                    <Value>0.000244140625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x08, 0x00, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>SW vol 1_3</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>ExtSWGainDB4</DetailedName>
                <Description>Ext vol (SW slew)( 1 ) : </Description>
                <ModuleParameter>
                    <Name>ExtSWGainDB4step</Name>
                    <Type>FixedPoint</Type>
                    <Address>205</Address>
                    <Value>0.000244140625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x08, 0x00, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>Auto EQ1</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>SingleBandSpkrEQAlgDP1</DetailedName>
                <Description>Single Band Automatic Speaker EQ( 1 ) : RespType_0[ 1 ],ResponseFile_0[ C:\Users\<USER>\Desktop\Lautsprecherbau\Messung\2912links2_MLSSA.txt ],Design_0[ False ],TargetResponseCsv_0[  ],DesignedFilters_0[ System.Collections.Generic.List`1[System.Collections.Generic.Dictionary`2[System.String,System.Object]] ],DesignAll[ False ],DesignInProgress[ False ],Frequency10[ 3222.9 ],Q0[ 1.7 ],Boost0[ -5.15 ],Type0[ PK ],Frequency11[ 13927.4 ],Q1[ 0.91 ],Boost1[ 3.56 ],Type1[ PK ],Frequency12[ 635 ],Q2[ 2.56 ],Boost2[ -6.53 ],Type2[ PK ],Frequency13[ 201.1 ],Q3[ 0.75 ],Boost3[ 1.58 ],Type3[ PK ],Frequency14[ 69 ],Q4[ 0.9 ],Boost4[ 1.98 ],Type4[ PK ],Frequency15[ 6808.4 ],Q5[ 3.58 ],Boost5[ -2.38 ],Type5[ PK ],ResponseType[ FreqResp ];</Description>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B0_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>109</Address>
                    <Value>1</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B1_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>110</Address>
                    <Value>0</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x00, 0x00, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1A1_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>111</Address>
                    <Value>0</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x00, 0x00, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B0_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>112</Address>
                    <Value>0.937643885612488</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x78, 0x04, 0xB7, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B1_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>113</Address>
                    <Value>-1.57027113437653</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x37, 0x01, 0x5B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1A1_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>114</Address>
                    <Value>1.57027113437653</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xC8, 0xFE, 0xA5, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B2_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>115</Address>
                    <Value>0.783536672592163</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x64, 0x4A, 0xEE, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1A2_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>116</Address>
                    <Value>-0.721180558204651</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0xA3, 0xB0, 0x5B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B0_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>117</Address>
                    <Value>1.15319359302521</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x93, 0x9B, 0xD9, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B1_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>118</Address>
                    <Value>0.348285317420959</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x2C, 0x94, 0x9D, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1A1_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>119</Address>
                    <Value>-0.348285317420959</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0xD3, 0x6B, 0x63, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B2_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>120</Address>
                    <Value>0.242023944854736</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x1E, 0xFA, 0xA4, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1A2_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>121</Address>
                    <Value>-0.395217537879944</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0xCD, 0x69, 0x83, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B0_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>122</Address>
                    <Value>0.987807750701904</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0x70, 0x7C, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B1_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>123</Address>
                    <Value>-1.94711327552795</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x06, 0xC4, 0xFE, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1A1_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>124</Address>
                    <Value>1.94711327552795</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xF9, 0x3B, 0x02, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B2_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>125</Address>
                    <Value>0.966051459312439</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7B, 0xA7, 0x93, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1A2_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>126</Address>
                    <Value>-0.953859210014343</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x85, 0xE7, 0xF1, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B0_4</Name>
                    <Type>FixedPoint</Type>
                    <Address>127</Address>
                    <Value>1.00314593315125</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x67, 0x16, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B1_4</Name>
                    <Type>FixedPoint</Type>
                    <Address>128</Address>
                    <Value>-1.96777987480164</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x04, 0x1F, 0xCA, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1A1_4</Name>
                    <Type>FixedPoint</Type>
                    <Address>129</Address>
                    <Value>1.96777987480164</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFB, 0xE0, 0x36, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B2_4</Name>
                    <Type>FixedPoint</Type>
                    <Address>130</Address>
                    <Value>0.965315937995911</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7B, 0x8F, 0x79, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1A2_4</Name>
                    <Type>FixedPoint</Type>
                    <Address>131</Address>
                    <Value>-0.968461871147156</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x84, 0x09, 0x71, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B0_5</Name>
                    <Type>FixedPoint</Type>
                    <Address>132</Address>
                    <Value>1.00114119052887</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x25, 0x65, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B1_5</Name>
                    <Type>FixedPoint</Type>
                    <Address>133</Address>
                    <Value>-1.99100422859192</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x01, 0x26, 0xC6, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1A1_5</Name>
                    <Type>FixedPoint</Type>
                    <Address>134</Address>
                    <Value>1.99100422859192</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFE, 0xD9, 0x3A, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B2_5</Name>
                    <Type>FixedPoint</Type>
                    <Address>135</Address>
                    <Value>0.989944219589233</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0xB6, 0x7E, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1A2_5</Name>
                    <Type>FixedPoint</Type>
                    <Address>136</Address>
                    <Value>-0.991085410118103</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x81, 0x24, 0x1D, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B0_6</Name>
                    <Type>FixedPoint</Type>
                    <Address>137</Address>
                    <Value>0.973447680473328</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7C, 0x99, 0xEF, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B1_6</Name>
                    <Type>FixedPoint</Type>
                    <Address>138</Address>
                    <Value>-1.11768138408661</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x70, 0xEF, 0xD1, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1A1_6</Name>
                    <Type>FixedPoint</Type>
                    <Address>139</Address>
                    <Value>1.11768138408661</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x8F, 0x10, 0x2F, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1B2_6</Name>
                    <Type>FixedPoint</Type>
                    <Address>140</Address>
                    <Value>0.804981589317322</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x67, 0x09, 0xA3, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP1A2_6</Name>
                    <Type>FixedPoint</Type>
                    <Address>141</Address>
                    <Value>-0.778429269790649</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x9C, 0x5C, 0x6E, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>Auto EQ1_2</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>SingleBandSpkrEQAlgDP2</DetailedName>
                <Description>Single Band Automatic Speaker EQ( 1 ) : RespType_0[ 1 ],ResponseFile_0[ C:\Users\<USER>\Desktop\Lautsprecherbau\Messung\2912links2_MLSSA.txt ],Design_0[ False ],TargetResponseCsv_0[  ],DesignedFilters_0[ System.Collections.Generic.List`1[System.Collections.Generic.Dictionary`2[System.String,System.Object]] ],DesignAll[ False ],DesignInProgress[ False ],Frequency10[ 3171.5 ],Q0[ 1.7 ],Boost0[ -6.53 ],Type0[ PK ],Frequency11[ 14267.5 ],Q1[ 0.91 ],Boost1[ 6.53 ],Type1[ PK ],Frequency12[ 624.9 ],Q2[ 2.56 ],Boost2[ -9.5 ],Type2[ PK ],Frequency13[ 201.1 ],Q3[ 0.75 ],Boost3[ 1.58 ],Type3[ PK ],Frequency14[ 69 ],Q4[ 0.9 ],Boost4[ 1.98 ],Type4[ PK ],Frequency15[ 6863.4 ],Q5[ 3.58 ],Boost5[ -3.37 ],Type5[ PK ],ResponseType[ FreqResp ];</Description>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B0_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>76</Address>
                    <Value>1</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B1_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>77</Address>
                    <Value>0</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x00, 0x00, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2A1_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>78</Address>
                    <Value>0</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x00, 0x00, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B0_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>79</Address>
                    <Value>0.922151684761047</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x76, 0x09, 0x11, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B1_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>80</Address>
                    <Value>-1.56052649021149</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x38, 0x40, 0xAB, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2A1_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>81</Address>
                    <Value>1.56052649021149</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xC7, 0xBF, 0x55, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B2_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>82</Address>
                    <Value>0.783236384391785</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x64, 0x41, 0x17, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2A2_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>83</Address>
                    <Value>-0.705388188362122</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0xA5, 0xB5, 0xD7, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B0_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>84</Address>
                    <Value>1.29716420173645</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xA6, 0x09, 0x7A, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B1_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>85</Address>
                    <Value>0.429860591888428</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x37, 0x05, 0xAC, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2A1_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>86</Address>
                    <Value>-0.429860591888428</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0xC8, 0xFA, 0x54, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B2_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>87</Address>
                    <Value>0.17256498336792</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x16, 0x16, 0x9C, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2A2_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>88</Address>
                    <Value>-0.46972918510437</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0xC3, 0xDF, 0xEA, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B0_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>89</Address>
                    <Value>0.982154607772827</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7D, 0xB7, 0x3E, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B1_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>90</Address>
                    <Value>-1.93982446193695</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x07, 0xB3, 0xD5, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2A1_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>91</Address>
                    <Value>1.93982446193695</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xF8, 0x4C, 0x2B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B2_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>92</Address>
                    <Value>0.964177846908569</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7B, 0x6A, 0x2E, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2A2_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>93</Address>
                    <Value>-0.946332454681396</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x86, 0xDE, 0x94, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B0_4</Name>
                    <Type>FixedPoint</Type>
                    <Address>94</Address>
                    <Value>1.00314593315125</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x67, 0x16, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B1_4</Name>
                    <Type>FixedPoint</Type>
                    <Address>95</Address>
                    <Value>-1.96777987480164</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x04, 0x1F, 0xCA, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2A1_4</Name>
                    <Type>FixedPoint</Type>
                    <Address>96</Address>
                    <Value>1.96777987480164</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFB, 0xE0, 0x36, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B2_4</Name>
                    <Type>FixedPoint</Type>
                    <Address>97</Address>
                    <Value>0.965315937995911</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7B, 0x8F, 0x79, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2A2_4</Name>
                    <Type>FixedPoint</Type>
                    <Address>98</Address>
                    <Value>-0.968461871147156</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x84, 0x09, 0x71, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B0_5</Name>
                    <Type>FixedPoint</Type>
                    <Address>99</Address>
                    <Value>1.00114119052887</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x25, 0x65, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B1_5</Name>
                    <Type>FixedPoint</Type>
                    <Address>100</Address>
                    <Value>-1.99100422859192</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x01, 0x26, 0xC6, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2A1_5</Name>
                    <Type>FixedPoint</Type>
                    <Address>101</Address>
                    <Value>1.99100422859192</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFE, 0xD9, 0x3A, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B2_5</Name>
                    <Type>FixedPoint</Type>
                    <Address>102</Address>
                    <Value>0.989944219589233</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0xB6, 0x7E, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2A2_5</Name>
                    <Type>FixedPoint</Type>
                    <Address>103</Address>
                    <Value>-0.991085410118103</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x81, 0x24, 0x1D, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B0_6</Name>
                    <Type>FixedPoint</Type>
                    <Address>104</Address>
                    <Value>0.962336421012878</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7B, 0x2D, 0xD7, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B1_6</Name>
                    <Type>FixedPoint</Type>
                    <Address>105</Address>
                    <Value>-1.0998010635376</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x73, 0x39, 0xB8, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2A1_6</Name>
                    <Type>FixedPoint</Type>
                    <Address>106</Address>
                    <Value>1.0998010635376</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x8C, 0xC6, 0x48, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2B2_6</Name>
                    <Type>FixedPoint</Type>
                    <Address>107</Address>
                    <Value>0.803421497344971</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x66, 0xD6, 0x84, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SingleBandSpkrEQAlgDP2A2_6</Name>
                    <Type>FixedPoint</Type>
                    <Address>108</Address>
                    <Value>-0.765758037567139</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x9D, 0xFB, 0xA4, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>2000</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>CrossoverFilter2WayAlgDP3</DetailedName>
                <Description>2-Way Crossover Filter - Double Precision( 1 ) : FrequencyLow[ 2200 ],FrequencyMidLow[ 250 ],FrequencyMidHigh[ 3000 ],FrequencyHigh[ 2200 ],Gain1[ 0 ],Gain2[ 0 ],Gain3[ 0 ],Type1[ 1 ],Type2[ 1 ],Type3[ 1 ],Type4[ 1 ],InvertLow[ False ],InvertHigh[ False ],Link1[ False ],Link2[ False ];</Description>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3LowInvert</Name>
                    <Type>FixedPoint</Type>
                    <Address>270</Address>
                    <Value>1</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3B0_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>271</Address>
                    <Value>0.0171465873718262</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x02, 0x31, 0xDC, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3B1_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>272</Address>
                    <Value>0.0342931747436523</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x04, 0x63, 0xB8, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3A1_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>273</Address>
                    <Value>1.59692943096161</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xCC, 0x68, 0x2F, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3B2_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>274</Address>
                    <Value>0.0171465873718262</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x02, 0x31, 0xDC, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3A2_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>275</Address>
                    <Value>-0.665515780448914</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0xAA, 0xD0, 0x61, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3B0_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>276</Address>
                    <Value>0.0171465873718262</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x02, 0x31, 0xDC, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3B1_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>277</Address>
                    <Value>0.0342931747436523</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x04, 0x63, 0xB8, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3A1_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>278</Address>
                    <Value>1.59692943096161</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xCC, 0x68, 0x2F, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3B2_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>279</Address>
                    <Value>0.0171465873718262</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x02, 0x31, 0xDC, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3A2_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>280</Address>
                    <Value>-0.665515780448914</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0xAA, 0xD0, 0x61, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3B0_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>281</Address>
                    <Value>0.815611362457275</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x68, 0x65, 0xF4, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3B1_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>282</Address>
                    <Value>-1.63122260570526</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x2F, 0x34, 0x19, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3A1_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>283</Address>
                    <Value>1.59692943096161</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xCC, 0x68, 0x2F, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3B2_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>284</Address>
                    <Value>0.815611362457275</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x68, 0x65, 0xF4, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3A2_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>285</Address>
                    <Value>-0.665515780448914</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0xAA, 0xD0, 0x61, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3B0_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>286</Address>
                    <Value>0.815611362457275</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x68, 0x65, 0xF4, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3B1_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>287</Address>
                    <Value>-1.63122260570526</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x2F, 0x34, 0x19, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3A1_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>288</Address>
                    <Value>1.59692943096161</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xCC, 0x68, 0x2F, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3B2_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>289</Address>
                    <Value>0.815611362457275</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x68, 0x65, 0xF4, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP3A2_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>290</Address>
                    <Value>-0.665515780448914</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0xAA, 0xD0, 0x61, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>1500</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>CrossoverFilter2WayAlgDP4</DetailedName>
                <Description>2-Way Crossover Filter - Double Precision( 1 ) : FrequencyLow[ 1450 ],FrequencyMidLow[ 250 ],FrequencyMidHigh[ 3000 ],FrequencyHigh[ 1800 ],Gain1[ 0 ],Gain2[ 0 ],Gain3[ 0 ],Type1[ 6 ],Type2[ 1 ],Type3[ 1 ],Type4[ 6 ],InvertLow[ False ],InvertHigh[ False ],Link1[ False ],Link2[ False ];</Description>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4LowInvert</Name>
                    <Type>FixedPoint</Type>
                    <Address>249</Address>
                    <Value>1</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4B0_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>250</Address>
                    <Value>0.00837481021881104</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x01, 0x12, 0x6D, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4B1_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>251</Address>
                    <Value>0.0167495012283325</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x02, 0x24, 0xD9, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4A1_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>252</Address>
                    <Value>1.83182501792908</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xEA, 0x79, 0x3E, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4B2_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>253</Address>
                    <Value>0.00837481021881104</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x01, 0x12, 0x6D, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4A2_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>254</Address>
                    <Value>-0.865324139595032</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x91, 0x3D, 0x0F, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4B0_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>255</Address>
                    <Value>0.00764656066894531</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0xFA, 0x90, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4B1_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>256</Address>
                    <Value>0.0152932405471802</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x01, 0xF5, 0x21, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4A1_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>257</Address>
                    <Value>1.67254793643951</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xD6, 0x16, 0x0D, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4B2_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>258</Address>
                    <Value>0.00764656066894531</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0xFA, 0x90, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4A2_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>259</Address>
                    <Value>-0.703134298324585</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0xA5, 0xFF, 0xB2, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4B0_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>260</Address>
                    <Value>0.905308604240417</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x73, 0xE1, 0x27, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4B1_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>261</Address>
                    <Value>-1.81061720848083</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x18, 0x3D, 0xB2, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4A1_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>262</Address>
                    <Value>1.78525304794312</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xE4, 0x83, 0x2C, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4B2_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>263</Address>
                    <Value>0.905308604240417</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x73, 0xE1, 0x27, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4A2_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>264</Address>
                    <Value>-0.835981369018555</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x94, 0xFE, 0x90, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4B0_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>265</Address>
                    <Value>0.811223864555359</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x67, 0xD6, 0x2F, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4B1_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>266</Address>
                    <Value>-1.62244784832001</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x30, 0x53, 0xA1, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4A1_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>267</Address>
                    <Value>1.59971964359283</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xCC, 0xC3, 0x9D, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4B2_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>268</Address>
                    <Value>0.811223864555359</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x67, 0xD6, 0x2F, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP4A2_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>269</Address>
                    <Value>-0.64517605304718</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0xAD, 0x6A, 0xDF, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>DC1</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>DCInpAlg1</DetailedName>
                <Description>dc Input( 1 ) : FormatL[ 0 ],FormatM[ 28 ],DC[ 3 ],IsDBSelected[ True ];</Description>
                <ModuleParameter>
                    <Name>DCInpAlg1</Name>
                    <Type>FixedPoint</Type>
                    <Address>0</Address>
                    <Value>3</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x00, 0x03, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>2nd Order Filter1</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>ParamToneIndexAlgFix1</DetailedName>
                <Description>Parameter Tone with Index Lookup Tables Fix( 1 ) : Steps[ 4 ],FrequencyLow[ 40 ],FrequencyHigh[ 100 ],Gain[ -5 ],Q[ 0.7071 ],Boost`min[ -10 ],Boost`max[ 10 ],Ripple[ 0.5 ],FilterType[ 1 ],Type[ 3 ];</Description>
                <ModuleParameter>
                    <Name>ParamToneIndexAlgFix1five</Name>
                    <Type>FixedPoint</Type>
                    <Address>142</Address>
                    <Value>5</Value>
                    <Size>4</Size>
                    <Data>0x02, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>ParamToneIndexAlgFix1_0b0</Name>
                    <Type>HexArray</Type>
                    <Address>143</Address>
                    <Size>80</Size>
                    <AddrIncr>4</AddrIncr>
                    <Data>0x00, 0x47, 0x51, 0x07, 0xFF, 0x71, 0x5D, 0xF1, 0x00, 0x47, 0x51, 0x07, 0x00, 0xFD, 0xA1, 0x6A, 0xFF, 0x82, 0x59, 0x05, 0x00, 0x47, 0x7D, 0x8F, 0xFF, 0x71, 0x04, 0xE2, 0x00, 0x47, 0x7D, 0x8F, 0x00, 0xFE, 0x41, 0x0F, 0xFF, 0x81, 0xBB, 0xEA, 0x00, 0x47, 0x9E, 0x70, 0xFF, 0x70, 0xC3, 0x1F, 0x00, 0x47, 0x9E, 0x70, 0x00, 0xFE, 0xB6, 0xB0, 0xFF, 0x81, 0x47, 0xAA, 0x00, 0x47, 0xB6, 0xB4, 0xFF, 0x70, 0x92, 0x98, 0x00, 0x47, 0xB6, 0xB4, 0x00, 0xFF, 0x0D, 0x5C, 0xFF, 0x80, 0xF1, 0xBF, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>Crossover1</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>CrossoverFilter2WayAlgDP1</DetailedName>
                <Description>2-Way Crossover Filter - Double Precision( 1 ) : FrequencyLow[ 130 ],FrequencyMidLow[ 250 ],FrequencyMidHigh[ 3000 ],FrequencyHigh[ 130 ],Gain1[ 0 ],Gain2[ 0 ],Gain3[ 0 ],Type1[ 1 ],Type2[ 1 ],Type3[ 1 ],Type4[ 1 ],InvertLow[ False ],InvertHigh[ False ],Link1[ False ],Link2[ False ];</Description>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1LowInvert</Name>
                    <Type>FixedPoint</Type>
                    <Address>163</Address>
                    <Value>1</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1LowInvert</Name>
                    <Type>FixedPoint</Type>
                    <Address>163</Address>
                    <Value>1</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B0_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>164</Address>
                    <Value>7.15255737304688E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x02, 0x58, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B0_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>164</Address>
                    <Value>7.15255737304688E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x02, 0x58, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B1_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>165</Address>
                    <Value>0.000143051147460938</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x04, 0xB0, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B1_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>165</Address>
                    <Value>0.000143051147460938</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x04, 0xB0, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1A1_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>166</Address>
                    <Value>1.97593557834625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFC, 0xEB, 0x75, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1A1_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>166</Address>
                    <Value>1.97593557834625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFC, 0xEB, 0x75, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B2_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>167</Address>
                    <Value>7.15255737304688E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x02, 0x58, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B2_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>167</Address>
                    <Value>7.15255737304688E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x02, 0x58, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1A2_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>168</Address>
                    <Value>-0.976221680641174</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x83, 0x0B, 0x2B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1A2_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>168</Address>
                    <Value>-0.976221680641174</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x83, 0x0B, 0x2B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B0_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>169</Address>
                    <Value>7.15255737304688E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x02, 0x58, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B0_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>169</Address>
                    <Value>7.15255737304688E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x02, 0x58, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B1_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>170</Address>
                    <Value>0.000143051147460938</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x04, 0xB0, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B1_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>170</Address>
                    <Value>0.000143051147460938</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x04, 0xB0, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1A1_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>171</Address>
                    <Value>1.97593557834625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFC, 0xEB, 0x75, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1A1_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>171</Address>
                    <Value>1.97593557834625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFC, 0xEB, 0x75, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B2_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>172</Address>
                    <Value>7.15255737304688E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x02, 0x58, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B2_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>172</Address>
                    <Value>7.15255737304688E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x02, 0x58, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1A2_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>173</Address>
                    <Value>-0.976221680641174</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x83, 0x0B, 0x2B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1A2_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>173</Address>
                    <Value>-0.976221680641174</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x83, 0x0B, 0x2B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B0_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>174</Address>
                    <Value>0.988039255142212</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0x78, 0x12, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B0_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>174</Address>
                    <Value>0.988039255142212</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0x78, 0x12, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B1_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>175</Address>
                    <Value>-1.97607862949371</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x03, 0x0F, 0xDB, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B1_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>175</Address>
                    <Value>-1.97607862949371</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x03, 0x0F, 0xDB, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1A1_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>176</Address>
                    <Value>1.97593557834625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFC, 0xEB, 0x75, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1A1_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>176</Address>
                    <Value>1.97593557834625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFC, 0xEB, 0x75, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B2_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>177</Address>
                    <Value>0.988039255142212</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0x78, 0x12, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B2_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>177</Address>
                    <Value>0.988039255142212</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0x78, 0x12, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1A2_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>178</Address>
                    <Value>-0.976221680641174</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x83, 0x0B, 0x2B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1A2_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>178</Address>
                    <Value>-0.976221680641174</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x83, 0x0B, 0x2B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B0_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>179</Address>
                    <Value>0.988039255142212</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0x78, 0x12, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B0_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>179</Address>
                    <Value>0.988039255142212</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0x78, 0x12, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B1_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>180</Address>
                    <Value>-1.97607862949371</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x03, 0x0F, 0xDB, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B1_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>180</Address>
                    <Value>-1.97607862949371</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x03, 0x0F, 0xDB, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1A1_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>181</Address>
                    <Value>1.97593557834625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFC, 0xEB, 0x75, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1A1_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>181</Address>
                    <Value>1.97593557834625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFC, 0xEB, 0x75, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B2_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>182</Address>
                    <Value>0.988039255142212</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0x78, 0x12, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1B2_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>182</Address>
                    <Value>0.988039255142212</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0x78, 0x12, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1A2_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>183</Address>
                    <Value>-0.976221680641174</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x83, 0x0B, 0x2B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP1A2_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>183</Address>
                    <Value>-0.976221680641174</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x83, 0x0B, 0x2B, </Data>
                </ModuleParameter>
            </Algorithm>
            <Algorithm>
                <AlgoName>ALG1</AlgoName>
                <DetailedName>CrossoverFilter2WayAlgDP2</DetailedName>
                <Description>2-Way Crossover Filter - Double Precision( 1 ) : FrequencyLow[ 130 ],FrequencyMidLow[ 250 ],FrequencyMidHigh[ 3000 ],FrequencyHigh[ 130 ],Gain1[ 0 ],Gain2[ 0 ],Gain3[ 0 ],Type1[ 1 ],Type2[ 1 ],Type3[ 1 ],Type4[ 1 ],InvertLow[ False ],InvertHigh[ False ],Link1[ False ],Link2[ False ];</Description>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2LowInvert</Name>
                    <Type>FixedPoint</Type>
                    <Address>184</Address>
                    <Value>1</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2LowInvert</Name>
                    <Type>FixedPoint</Type>
                    <Address>184</Address>
                    <Value>1</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B0_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>185</Address>
                    <Value>7.15255737304688E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x02, 0x58, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B0_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>185</Address>
                    <Value>7.15255737304688E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x02, 0x58, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B1_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>186</Address>
                    <Value>0.000143051147460938</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x04, 0xB0, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B1_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>186</Address>
                    <Value>0.000143051147460938</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x04, 0xB0, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2A1_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>187</Address>
                    <Value>1.97593557834625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFC, 0xEB, 0x75, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2A1_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>187</Address>
                    <Value>1.97593557834625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFC, 0xEB, 0x75, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B2_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>188</Address>
                    <Value>7.15255737304688E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x02, 0x58, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B2_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>188</Address>
                    <Value>7.15255737304688E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x02, 0x58, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2A2_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>189</Address>
                    <Value>-0.976221680641174</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x83, 0x0B, 0x2B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2A2_0</Name>
                    <Type>FixedPoint</Type>
                    <Address>189</Address>
                    <Value>-0.976221680641174</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x83, 0x0B, 0x2B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B0_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>190</Address>
                    <Value>7.15255737304688E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x02, 0x58, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B0_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>190</Address>
                    <Value>7.15255737304688E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x02, 0x58, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B1_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>191</Address>
                    <Value>0.000143051147460938</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x04, 0xB0, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B1_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>191</Address>
                    <Value>0.000143051147460938</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x04, 0xB0, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2A1_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>192</Address>
                    <Value>1.97593557834625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFC, 0xEB, 0x75, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2A1_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>192</Address>
                    <Value>1.97593557834625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFC, 0xEB, 0x75, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B2_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>193</Address>
                    <Value>7.15255737304688E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x02, 0x58, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B2_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>193</Address>
                    <Value>7.15255737304688E-05</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x02, 0x58, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2A2_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>194</Address>
                    <Value>-0.976221680641174</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x83, 0x0B, 0x2B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2A2_1</Name>
                    <Type>FixedPoint</Type>
                    <Address>194</Address>
                    <Value>-0.976221680641174</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x83, 0x0B, 0x2B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B0_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>195</Address>
                    <Value>0.988039255142212</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0x78, 0x12, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B0_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>195</Address>
                    <Value>0.988039255142212</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0x78, 0x12, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B1_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>196</Address>
                    <Value>-1.97607862949371</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x03, 0x0F, 0xDB, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B1_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>196</Address>
                    <Value>-1.97607862949371</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x03, 0x0F, 0xDB, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2A1_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>197</Address>
                    <Value>1.97593557834625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFC, 0xEB, 0x75, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2A1_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>197</Address>
                    <Value>1.97593557834625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFC, 0xEB, 0x75, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B2_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>198</Address>
                    <Value>0.988039255142212</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0x78, 0x12, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B2_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>198</Address>
                    <Value>0.988039255142212</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0x78, 0x12, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2A2_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>199</Address>
                    <Value>-0.976221680641174</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x83, 0x0B, 0x2B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2A2_2</Name>
                    <Type>FixedPoint</Type>
                    <Address>199</Address>
                    <Value>-0.976221680641174</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x83, 0x0B, 0x2B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B0_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>200</Address>
                    <Value>0.988039255142212</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0x78, 0x12, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B0_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>200</Address>
                    <Value>0.988039255142212</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0x78, 0x12, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B1_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>201</Address>
                    <Value>-1.97607862949371</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x03, 0x0F, 0xDB, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B1_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>201</Address>
                    <Value>-1.97607862949371</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x03, 0x0F, 0xDB, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2A1_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>202</Address>
                    <Value>1.97593557834625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFC, 0xEB, 0x75, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2A1_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>202</Address>
                    <Value>1.97593557834625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xFC, 0xEB, 0x75, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B2_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>203</Address>
                    <Value>0.988039255142212</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0x78, 0x12, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2B2_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>203</Address>
                    <Value>0.988039255142212</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x7E, 0x78, 0x12, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2A2_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>204</Address>
                    <Value>-0.976221680641174</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x83, 0x0B, 0x2B, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>CrossoverFilter2WayAlgDP2A2_3</Name>
                    <Type>FixedPoint</Type>
                    <Address>204</Address>
                    <Value>-0.976221680641174</Value>
                    <Size>4</Size>
                    <Data>0x0F, 0x83, 0x0B, 0x2B, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>St Mixer1</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>StereoMixerAlgSlew1</DetailedName>
                <Description>Stereo Mixer SW Slew( 2 ) : Gain`max[ 6 ],Gain`min[ -30 ],Gain`res[ 36 ],Gain[ 0 ];Gain`max[ 6 ],Gain`min[ -30 ],Gain`res[ 36 ],Gain[ 0 ];</Description>
                <ModuleParameter>
                    <Name>StereoMixerAlgSlew12</Name>
                    <Type>FixedPoint</Type>
                    <Address>207</Address>
                    <Value>1</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>StereoMixerAlgSlew14</Name>
                    <Type>FixedPoint</Type>
                    <Address>208</Address>
                    <Value>1</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>SW vol 1</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>SWGain1940DBAlg1</DetailedName>
                <Description>Gain (RC Slew)( 1 ) : Gain`max[ 20 ],Gain`min[ -20 ],Gain`res[ 50 ],Gain[ 5.6 ];</Description>
                <ModuleParameter>
                    <Name>SWGain1940DBAlg1target</Name>
                    <Type>FixedPoint</Type>
                    <Address>4</Address>
                    <Value>1.90546071529388</Value>
                    <Size>4</Size>
                    <Data>0x00, 0xF3, 0xE6, 0x23, </Data>
                </ModuleParameter>
                <ModuleParameter>
                    <Name>SWGain1940DBAlg1step</Name>
                    <Type>FixedPoint</Type>
                    <Address>5</Address>
                    <Value>0.000244140625</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x00, 0x08, 0x00, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
        <Module>
            <CellName>Gain1</CellName>
            <Algorithm>
                <AlgoName>ALG0</AlgoName>
                <DetailedName>Gain1940AlgNS1</DetailedName>
                <Description>Gain (no slew)( 1 ) : Gain[ 1 ],dBLinearLabel[ 1 ];</Description>
                <ModuleParameter>
                    <Name>Gain1940AlgNS1</Name>
                    <Type>FixedPoint</Type>
                    <Address>1</Address>
                    <Value>1</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
            </Algorithm>
            <Algorithm>
                <AlgoName>ALG1</AlgoName>
                <DetailedName>Gain1940AlgNS2</DetailedName>
                <Description>Gain (no slew)( 1 ) : Gain[ 1 ],dBLinearLabel[ 1 ];</Description>
                <ModuleParameter>
                    <Name>Gain1940AlgNS2</Name>
                    <Type>FixedPoint</Type>
                    <Address>2</Address>
                    <Value>1</Value>
                    <Size>4</Size>
                    <Data>0x00, 0x80, 0x00, 0x00, </Data>
                </ModuleParameter>
            </Algorithm>
        </Module>
    </IC>
</Schematic>