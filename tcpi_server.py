import gc
import json


try:
    from .. import config
    from ..protocols.protocol import CONTROL_CODES
    from ..networking.socket_server import SocketServer
    from ..eeprom_manager import EEPROMManager

except:
    import config
    from protocol import CONTROL_CODES
    from socket_server import SocketServer
    try:
        from eeprom_manager import EEPROMManager
    except ImportError:
        EEPROMManager = None



class TCPiServer(SocketServer):
    PROPERTIES_FILE_NAME = 'properties.json'


    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        self.add_subscriber(self._propertize)


    @property
    def properties(self):
        with open(self.PROPERTIES_FILE_NAME, 'tr') as f:
            return json.load(f)


    @properties.setter
    def properties(self, properties):
        with open(self.PROPERTIES_FILE_NAME, 'tw') as f:
            json.dump(properties, f)


    def _propertize(self, data):
        if data == config.CMD_GET_PROTERTIES:
            self.send(json.dumps(self.properties).encode())

        if data[:len(config.CMD_SET_PROTERTIES)] == config.CMD_SET_PROTERTIES:
            self.properties = json.loads(data.decode().split('=')[-1].strip())



class Bus(TCPiServer):

    def __init__(self, bus, class_finder, **kwargs):
        super().__init__(**kwargs)

        self._bus = bus
        self._class_finder = class_finder
        self.actions = {CONTROL_CODES['Write']      : self.write,
                        CONTROL_CODES['ReadRequest']: self.read}

        self.add_subscriber(self._process_data)


    def write(self, packet):
        raise NotImplementedError


    def read(self, packet_request):
        raise NotImplementedError


    @staticmethod
    def _load_packet_data(cls_packet, data):
        packet = cls_packet()
        remains = packet.load_bytes(data)

        return packet, remains


    def _process_data(self, data):

        while len(data) > 0:
            control_code = data[0]
            cls = self._class_finder.get(control_code)

            if cls:
                packet, data = self._load_packet_data(cls, data)

                if self.DEBUG_MODE:
                    packet.print()

                action = self.actions[control_code]
                action(packet)

            else:
                print('Unknown data: ', data)
                break

        if config.IS_MICROPYTHON:
            gc.collect()

            if self.DEBUG_MODE:
                print('\n[Memory - free: {}   allocated: {}]'.format(gc.mem_free(), gc.mem_alloc()))



class I2C(Bus):
    I2C_ADDRESS = 0x68 >> 1
    I2C_ADDRESS_EEPROM = 0xA0 >> 1

    I2C_ADDRESSes = {1                 : (I2C_ADDRESS, 2),  # (i2c_addresses, n_sub_address_bytes)
                     2                 : (I2C_ADDRESS_EEPROM, 2),
                     I2C_ADDRESS       : (I2C_ADDRESS, 2),
                     I2C_ADDRESS_EEPROM: (I2C_ADDRESS_EEPROM, 2)}


    def __init__(self, bus, class_finder, i2c_addresses = I2C_ADDRESSes, **kwargs):
        super().__init__(bus, class_finder, **kwargs)

        self._i2c_addresses = i2c_addresses

        # EEPROM Manager initialisieren
        if EEPROMManager is not None:
            self.eeprom_manager = EEPROMManager(bus, self.I2C_ADDRESS_EEPROM)
        else:
            self.eeprom_manager = None

        # Zusätzliche Subscriber für EEPROM-Befehle
        self.add_subscriber(self._handle_eeprom_commands)


    def _get_i2c_address(self, packet):
        addr = packet.elements['Chip_address'].value
        return self._i2c_addresses[addr] if addr in self.I2C_ADDRESSes else (addr, 1)


    def write(self, packet):
        i2c_address, n_sub_address_bytes = self._get_i2c_address(packet)
        self._bus.write_addressed_bytes(i2c_address = i2c_address,
                                        sub_address = packet.elements['Address'].value,
                                        bytes_array = packet.data,
                                        n_sub_address_bytes = n_sub_address_bytes)


    def read(self, packet_request):
        # hardware read =================
        i2c_address, n_sub_address_bytes = self._get_i2c_address(packet_request)
        result = self._bus.read_addressed_bytes(i2c_address = i2c_address,
                                                sub_address = packet_request.elements['Address'].value,
                                                n_bytes = packet_request.elements['Data_length'].value,
                                                n_sub_address_bytes = n_sub_address_bytes)
        # send response packet ==========
        cls = self._class_finder[CONTROL_CODES['ReadResponse']]
        packet_response = cls(chip_address = packet_request.elements['Chip_address'].value,
                              sub_address = packet_request.elements['Address'].value,
                              data = result if result is not None else b'', success = result is not None)

        if self.DEBUG_MODE:
            packet_response.print()

        self.send(packet_response.bytes)

        return result

    def _handle_eeprom_commands(self, data):
        """Behandelt spezielle EEPROM-Befehle"""
        if not self.eeprom_manager:
            return

        try:
            # EEPROM Write Command
            if data.startswith(config.CMD_EEPROM_WRITE):
                self._handle_eeprom_write_command(data)

            # HEX File Programming Command
            elif data.startswith(config.CMD_EEPROM_PROGRAM_HEX):
                self._handle_hex_program_command(data)

        except Exception as e:
            print(f"EEPROM Command Error: {e}")

    def _handle_eeprom_write_command(self, data):
        """
        Behandelt EEPROM_WRITE Befehl
        Format: EEPROM_WRITE:<address>:<hex_data>
        """
        try:
            command_str = data.decode('utf-8')
            parts = command_str.split(':')

            if len(parts) != 3:
                print("Ungültiges EEPROM_WRITE Format")
                return

            address = int(parts[1], 16)
            hex_data = parts[2]
            binary_data = bytes.fromhex(hex_data)

            print(f"EEPROM Write: Adresse 0x{address:04X}, {len(binary_data)} Bytes")

            success = self.eeprom_manager.write_data(address, binary_data)

            # Antwort senden
            response = b"EEPROM_WRITE_OK" if success else b"EEPROM_WRITE_ERROR"
            self.send(response)

        except Exception as e:
            print(f"EEPROM Write Error: {e}")
            self.send(b"EEPROM_WRITE_ERROR")

    def _handle_hex_program_command(self, data):
        """
        Behandelt PROGRAM_HEX Befehl
        Format: PROGRAM_HEX:<hex_file_content>
        """
        try:
            command_str = data.decode('utf-8')

            if ':' not in command_str:
                print("Ungültiges PROGRAM_HEX Format")
                return

            hex_content = command_str.split(':', 1)[1]

            print("Programmiere HEX-Datei ins EEPROM...")

            success = self.eeprom_manager.program_hex_file(hex_content)

            # Antwort senden
            response = b"PROGRAM_HEX_OK" if success else b"PROGRAM_HEX_ERROR"
            self.send(response)

        except Exception as e:
            print(f"HEX Program Error: {e}")
            self.send(b"PROGRAM_HEX_ERROR")
