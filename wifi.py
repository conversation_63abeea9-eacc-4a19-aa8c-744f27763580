import config
import network

import led



# WiFi network _________________________
def wait_for_wifi():
    import time

    sta_if = network.WLAN(network.STA_IF)

    # WiFi-Status prüfen
    if sta_if.isconnected():
        print('Already connected to WiFi')
        print('Network configuration:', sta_if.ifconfig())
        led.blink_on_board_led(times = 2)
        return True

    print('connecting to network...')
    print(f'SSID: {config.SSID}')
    print(f'Password: {"*" * len(config.PASSWORD)}')

    # WiFi aktivieren
    sta_if.active(True)
    time.sleep(1)

    # Verfügbare Netzwerke scannen
    print('Scanning for networks...')
    networks = sta_if.scan()
    print('Available networks:')
    for net in networks:
        ssid = net[0].decode('utf-8')
        print(f'  - {ssid} (Signal: {net[3]} dBm)')

    # Prüfen ob unser SSID verfügbar ist
    target_found = False
    for net in networks:
        if net[0].decode('utf-8') == config.SSID:
            target_found = True
            print(f'✓ Target network "{config.SSID}" found!')
            break

    if not target_found:
        print(f'✗ Target network "{config.SSID}" not found!')
        print('Please check SSID in config.py')
        return False

    # Verbindung versuchen
    print(f'Connecting to {config.SSID}...')
    sta_if.connect(config.SSID, config.PASSWORD)

    # Warten mit Timeout
    max_wait = 20  # 20 Sekunden Timeout
    wait_count = 0

    while not sta_if.isconnected() and wait_count < max_wait:
        print(f'Waiting... ({wait_count + 1}/{max_wait})')
        time.sleep(1)
        wait_count += 1

    # Ergebnis prüfen
    if sta_if.isconnected():
        print('✓ WiFi connected successfully!')
        print('Network configuration:', sta_if.ifconfig())
        led.blink_on_board_led(times = 2)
        return True
    else:
        print('✗ WiFi connection failed!')
        print('Status:', sta_if.status())

        # Status-Codes erklären
        status_codes = {
            0: 'IDLE',
            1: 'CONNECTING',
            2: 'WRONG_PASSWORD',
            3: 'NO_AP_FOUND',
            4: 'CONNECT_FAIL',
            5: 'GOT_IP'
        }

        status = sta_if.status()
        status_text = status_codes.get(status, f'UNKNOWN({status})')
        print(f'Status code: {status} ({status_text})')

        if status == 2:
            print('→ Check WiFi password in config.py')
        elif status == 3:
            print('→ Check WiFi SSID in config.py')
        elif status == 4:
            print('→ Check WiFi settings and signal strength')

        return False
