#!/usr/bin/env python3
"""
EEPROM Dump Tool - Liest EEPROM-Inhalt aus und analysiert ihn
"""

import socket
import sys
from pathlib import Path


def dump_eeprom(host, port=8086, start_address=0x0000, length=4096):
    """Liest EEPROM-Inhalt vom ESP32 aus"""
    
    print(f"Verbinde zu {host}:{port}...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect((host, port))
        
        print(f"Lese EEPROM: 0x{start_address:04X} - 0x{start_address + length - 1:04X} ({length} Bytes)")
        
        # EEPROM_DUMP Befehl senden
        command = f"EEPROM_DUMP:{start_address:04X}:{length}".encode('utf-8')
        sock.send(command)
        
        # Antwort empfangen
        response = sock.recv(1024)
        print(f"Response: {response}")
        
        sock.close()
        
        if response == b"EEPROM_DUMP_OK":
            print("✓ EEPROM-Dump erfolgreich (siehe ESP32-Logs)")
            return True
        else:
            print(f"✗ EEPROM-Dump fehlgeschlagen: {response}")
            return False
            
    except Exception as e:
        print(f"Fehler: {e}")
        return False


def analyze_hex_data(hex_file):
    """Analysiert die ursprünglichen HEX-Daten"""
    
    print(f"\n=== Analyse: {hex_file} ===")
    
    if not Path(hex_file).exists():
        print(f"Datei nicht gefunden: {hex_file}")
        return
    
    with open(hex_file, 'r') as f:
        content = f.read()
    
    print(f"Dateigröße: {len(content)} Zeichen")
    
    # Intel HEX analysieren
    lines = content.strip().split('\n')
    data_bytes = 0
    address_ranges = []
    
    for line in lines:
        if line.startswith(':'):
            try:
                length = int(line[1:3], 16)
                address = int(line[3:7], 16)
                record_type = int(line[7:9], 16)
                
                if record_type == 0x00:  # Data Record
                    data_bytes += length
                    address_ranges.append((address, address + length - 1))
                    
            except:
                continue
    
    print(f"Intel HEX Zeilen: {len(lines)}")
    print(f"Daten-Bytes: {data_bytes}")
    
    if address_ranges:
        min_addr = min(addr[0] for addr in address_ranges)
        max_addr = max(addr[1] for addr in address_ranges)
        print(f"Adress-Bereich: 0x{min_addr:04X} - 0x{max_addr:04X}")
    
    # Erste Bytes anzeigen
    if lines and lines[0].startswith(':'):
        try:
            first_line = lines[1] if len(lines) > 1 else lines[0]  # Skip Extended Address
            if first_line.startswith(':') and first_line[7:9] == '00':
                data_part = first_line[9:-2]  # Remove checksum
                first_bytes = [data_part[i:i+2] for i in range(0, min(32, len(data_part)), 2)]
                print(f"Erste 16 Bytes: {' '.join(first_bytes)}")
        except:
            pass


def compare_original_vs_converted():
    """Vergleicht Original C-Array mit konvertierter Intel HEX"""
    
    print("\n=== Vergleich Original vs. Konvertiert ===")
    
    # Original C-Array analysieren
    if Path("test_sigma.hex").exists():
        print("\n--- Original C-Array (test_sigma.hex) ---")
        
        with open("test_sigma.hex", 'r') as f:
            content = f.read()
        
        # Erste Hex-Werte extrahieren
        import re
        hex_values = re.findall(r'0x([0-9A-Fa-f]{2})', content[:200])  # Erste 200 Zeichen
        
        if hex_values:
            print(f"Erste 16 Bytes: {' '.join(hex_values[:16])}")
            print(f"Gesamt gefunden: {len(re.findall(r'0x([0-9A-Fa-f]{2})', content))} Hex-Werte")
    
    # Konvertierte Intel HEX analysieren
    converted_files = ["test_sigma_converted.hex", "test_sigma.hex"]
    
    for filename in converted_files:
        if Path(filename).exists() and filename != "test_sigma.hex":
            print(f"\n--- Konvertierte Intel HEX ({filename}) ---")
            analyze_hex_data(filename)
            break


def check_sigma_studio_export_format():
    """Prüft das Sigma Studio Export-Format"""
    
    print("\n=== Sigma Studio Export Format Check ===")
    
    # Prüfe verschiedene Export-Dateien
    export_files = [
        "test_sigma.hex",
        "test_sigma.params", 
        "test_sigma.xml",
        "test_sigma_IC_1.h",
        "test_sigma_IC_1_PARAM.h",
        "test_sigma_IC_1_REG.h"
    ]
    
    for filename in export_files:
        if Path(filename).exists():
            file_size = Path(filename).stat().st_size
            print(f"✓ {filename}: {file_size} Bytes")
            
            # Erste Zeilen anzeigen
            if filename.endswith('.hex') and 'IC_' not in filename:
                with open(filename, 'r') as f:
                    first_lines = [f.readline().strip() for _ in range(3)]
                print(f"  Erste Zeilen: {first_lines}")
        else:
            print(f"✗ {filename}: Nicht gefunden")


def suggest_debugging_steps():
    """Schlägt weitere Debugging-Schritte vor"""
    
    print("\n=== Debugging-Empfehlungen ===")
    
    print("1. EEPROM-Inhalt auslesen:")
    print("   python eeprom_dump_tool.py --dump --host **************")
    
    print("\n2. Sigma Studio Projekt prüfen:")
    print("   - Ist das Projekt für Self-Boot konfiguriert?")
    print("   - Sind alle Ein-/Ausgänge korrekt zugewiesen?")
    print("   - Funktioniert das Projekt im Live-Modus?")
    
    print("\n3. ADAU1701 Hardware prüfen:")
    print("   - SELFBOOT Pin-Konfiguration")
    print("   - EEPROM-Verbindung (I2C)")
    print("   - Reset-Timing")
    
    print("\n4. Alternative Tests:")
    print("   - Originales USBI-JTAG zum Vergleich")
    print("   - Einfaches Test-Projekt (nur Durchgang)")
    print("   - EEPROM löschen und neu programmieren")


def main():
    """Hauptfunktion"""
    
    if len(sys.argv) > 1 and sys.argv[1] == "--dump":
        host = sys.argv[2] if len(sys.argv) > 2 else "**************"
        dump_eeprom(host)
        return
    
    print("EEPROM Debug Tool")
    print("=" * 50)
    
    # Dateien analysieren
    analyze_hex_data("test_intel.hex")
    
    if Path("test_sigma_converted.hex").exists():
        analyze_hex_data("test_sigma_converted.hex")
    
    # Vergleich
    compare_original_vs_converted()
    
    # Export-Format prüfen
    check_sigma_studio_export_format()
    
    # Empfehlungen
    suggest_debugging_steps()


if __name__ == "__main__":
    main()
