"""
Vereinfachter EEPROM Manager für MicroPython
Speziell für ESP32 und ADAU1701 EEPROM-Programmierung
"""

import time
import gc


class SimpleHexParser:
    """Vereinfachter HEX-Parser für MicroPython"""
    
    def __init__(self):
        self.data = bytearray()
        self.base_address = 0
    
    def parse_hex_file(self, hex_content):
        """Parst eine Sigma Studio HEX-Datei"""
        self.data = bytearray()
        self.base_address = 0
        
        lines = hex_content.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or not line.startswith(':'):
                continue
            
            try:
                self._parse_hex_line(line)
            except Exception as e:
                print(f"HEX Parse Error: {e}")
                continue
        
        return self.data
    
    def _parse_hex_line(self, line):
        """Parst eine einzelne HEX-Zeile"""
        hex_data = line[1:]  # ':' entfernen
        
        if len(hex_data) < 8:
            return
        
        # <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typ extrahieren
        length = int(hex_data[0:2], 16)
        address = int(hex_data[2:6], 16)
        record_type = int(hex_data[6:8], 16)
        
        # Daten extrahieren
        data_start = 8
        data_end = data_start + (length * 2)
        
        if len(hex_data) < data_end:
            return
        
        data_hex = hex_data[data_start:data_end]
        
        # Record Types verarbeiten
        if record_type == 0x00:  # Data Record
            self._process_data_record(address, data_hex)
        elif record_type == 0x02:  # Extended Segment Address
            self.base_address = int(data_hex, 16) << 4
        elif record_type == 0x04:  # Extended Linear Address
            self.base_address = int(data_hex, 16) << 16
    
    def _process_data_record(self, address, data_hex):
        """Verarbeitet einen Daten-Record"""
        full_address = self.base_address + address
        
        # Daten in Bytes konvertieren
        data_bytes = bytes.fromhex(data_hex)
        
        # Array erweitern falls nötig
        required_size = full_address + len(data_bytes)
        if len(self.data) < required_size:
            self.data.extend(b'\xFF' * (required_size - len(self.data)))
        
        # Daten einfügen
        for i, byte_val in enumerate(data_bytes):
            self.data[full_address + i] = byte_val


class SimpleEEPROMManager:
    """Vereinfachter EEPROM Manager für MicroPython"""
    
    def __init__(self, i2c_bus, eeprom_address=0x50, page_size=32):
        self.i2c_bus = i2c_bus
        self.eeprom_address = eeprom_address
        self.page_size = page_size
        self.write_delay_ms = 100  # Erhöht für ADAU1701 Stabilität
    
    def write_page(self, address, data):
        """Schreibt eine Page in das EEPROM"""
        if len(data) > self.page_size:
            print(f"Data too large for page: {len(data)} > {self.page_size}")
            return False
        
        try:
            self.i2c_bus.write_addressed_bytes(
                i2c_address=self.eeprom_address,
                sub_address=address,
                bytes_array=data,
                n_sub_address_bytes=2
            )
            
            time.sleep(self.write_delay_ms / 1000.0)
            return True
            
        except Exception as e:
            print(f"EEPROM Write Error at 0x{address:04X}: {e}")
            return False
    
    def write_data(self, start_address, data, verify=True):
        """Schreibt Daten in das EEPROM"""
        if not data:
            return True
        
        print(f"Writing {len(data)} bytes to 0x{start_address:04X}")
        
        offset = 0
        while offset < len(data):
            current_address = start_address + offset
            
            # Page-Grenze berechnen
            page_start = (current_address // self.page_size) * self.page_size
            bytes_to_page_end = self.page_size - (current_address - page_start)
            
            # Anzahl Bytes für diesen Write
            bytes_to_write = min(bytes_to_page_end, len(data) - offset)
            page_data = data[offset:offset + bytes_to_write]
            
            print(f"  Page 0x{current_address:04X}: {bytes_to_write} bytes")
            
            if not self.write_page(current_address, page_data):
                print(f"Error writing page 0x{current_address:04X}")
                return False
            
            offset += bytes_to_write
            
            # Memory cleanup für MicroPython
            if offset % (self.page_size * 4) == 0:
                gc.collect()
        
        # Verifizierung
        if verify:
            print("Verifying...")
            if not self.verify_data(start_address, data):
                print("Verification failed!")
                return False
            print("Verification OK!")
        
        return True
    
    def read_data(self, address, length):
        """Liest Daten aus dem EEPROM"""
        try:
            return self.i2c_bus.read_addressed_bytes(
                i2c_address=self.eeprom_address,
                sub_address=address,
                n_bytes=length,
                n_sub_address_bytes=2
            )
        except Exception as e:
            print(f"EEPROM Read Error at 0x{address:04X}: {e}")
            return None
    
    def verify_data(self, address, expected_data):
        """Verifiziert geschriebene Daten"""
        # Verifizierung in kleineren Blöcken für MicroPython
        block_size = 64
        
        for offset in range(0, len(expected_data), block_size):
            end_offset = min(offset + block_size, len(expected_data))
            block_size_actual = end_offset - offset
            
            read_data = self.read_data(address + offset, block_size_actual)
            if read_data is None:
                return False
            
            expected_block = expected_data[offset:end_offset]
            if bytes(read_data) != expected_block:
                print(f"Verification failed at offset {offset}")
                return False
            
            # Memory cleanup
            gc.collect()
        
        return True
    
    def program_hex_content(self, hex_content, start_address=0):
        """Programmiert HEX-Content ins EEPROM"""
        try:
            parser = SimpleHexParser()
            eeprom_data = parser.parse_hex_file(hex_content)
            
            if not eeprom_data:
                print("No data in HEX content")
                return False
            
            print(f"HEX parsed: {len(eeprom_data)} bytes")
            
            return self.write_data(start_address, bytes(eeprom_data))
            
        except Exception as e:
            print(f"Error programming HEX: {e}")
            return False
    
    def dump_eeprom(self, start_address=0, length=256):
        """Liest EEPROM-Bereich aus (für Debugging)"""
        print(f"EEPROM Dump: 0x{start_address:04X} - 0x{start_address + length - 1:04X}")
        
        data = self.read_data(start_address, length)
        if data is None:
            return None
        
        # Hex-Dump ausgeben
        for i in range(0, len(data), 16):
            line_data = data[i:i+16]
            hex_str = ' '.join(f'{b:02X}' for b in line_data)
            print(f"{start_address + i:04X}: {hex_str}")
        
        return data


# Test-Funktion
def test_hex_parser():
    """Testet den HEX-Parser"""
    test_hex = """
:020000040000FA
:10000000010203040506070809101112131415161F
:10001000171819202122232425262728293031323E
:00000001FF
"""
    
    parser = SimpleHexParser()
    data = parser.parse_hex_file(test_hex)
    print(f"Test HEX parsed: {len(data)} bytes")
    print(f"First 16 bytes: {data[:16].hex()}")
    return len(data) > 0


if __name__ == "__main__":
    # Test ausführen
    test_hex_parser()
