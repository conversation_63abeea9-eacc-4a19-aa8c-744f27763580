#!/usr/bin/env python3
"""
Test-Tool für HEX-zu-BIN Konvertierung
Testet speziell den Export aus Sigma Studio und die Konvertierung
"""

import sys
from pathlib import Path
import binascii

# Lokale Module
try:
    from hex_parser import SigmaStudioHexParser
    from eeprom_upy import SimpleHexParser
except ImportError as e:
    print(f"Import-Fehler: {e}")
    sys.exit(1)


def create_sigma_studio_test_hex():
    """Erstellt eine realistische Sigma Studio HEX-Datei zum Testen"""
    
    # Typische Sigma Studio HEX-Struktur
    sigma_hex = """
:020000040000FA
:20000000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC0
:20002000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA0
:20004000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF80
:20006000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF60
:20008000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF40
:2000A000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF20
:2000C000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00
:2000E000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE0
:20010000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFBF
:20012000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF9F
:20014000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7F
:20016000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF5F
:20018000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF3F
:2001A000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF1F
:2001C000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFDFF
:2001E000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFDDF
:20020000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE9F
:20022000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE7F
:20024000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE5F
:20026000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE3F
:20028000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE1F
:2002A000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFDFF
:2002C000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFDDF
:2002E000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFDDF
:20030000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD9F
:20032000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD7F
:20034000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD5F
:20036000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD3F
:20038000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD1F
:2003A000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFDFF
:2003C000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFDDF
:2003E000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFDDF
:00000001FF
""".strip()
    
    # Als Datei speichern
    with open("sigma_studio_test.hex", 'w') as f:
        f.write(sigma_hex)
    
    print("✓ Sigma Studio Test-HEX erstellt: sigma_studio_test.hex")
    return sigma_hex


def test_hex_parser_comparison():
    """Vergleicht beide HEX-Parser"""
    print("\n=== Vergleich HEX-Parser ===")
    
    hex_content = create_sigma_studio_test_hex()
    
    # Test mit vollständigem Parser
    try:
        parser1 = SigmaStudioHexParser()
        data1 = parser1.parse_hex_file(hex_content)
        print(f"✓ Vollständiger Parser: {len(data1)} Bytes")
    except Exception as e:
        print(f"✗ Vollständiger Parser Fehler: {e}")
        data1 = None
    
    # Test mit vereinfachtem Parser (MicroPython)
    try:
        parser2 = SimpleHexParser()
        data2 = parser2.parse_hex_file(hex_content)
        print(f"✓ Vereinfachter Parser: {len(data2)} Bytes")
    except Exception as e:
        print(f"✗ Vereinfachter Parser Fehler: {e}")
        data2 = None
    
    # Vergleich
    if data1 and data2:
        if bytes(data1) == bytes(data2):
            print("✓ Beide Parser liefern identische Ergebnisse!")
        else:
            print("✗ Parser-Ergebnisse unterscheiden sich!")
            print(f"  Unterschied bei Byte: {find_first_difference(data1, data2)}")
    
    return data1, data2


def find_first_difference(data1, data2):
    """Findet den ersten Unterschied zwischen zwei Byte-Arrays"""
    min_len = min(len(data1), len(data2))
    
    for i in range(min_len):
        if data1[i] != data2[i]:
            return i
    
    if len(data1) != len(data2):
        return min_len
    
    return -1  # Keine Unterschiede


def analyze_hex_structure(hex_content):
    """Analysiert die Struktur einer HEX-Datei"""
    print("\n=== HEX-Datei Struktur-Analyse ===")
    
    lines = hex_content.strip().split('\n')
    record_types = {}
    total_data_bytes = 0
    address_ranges = []
    
    for line_num, line in enumerate(lines, 1):
        line = line.strip()
        if not line.startswith(':'):
            continue
        
        try:
            # Record-Typ extrahieren
            record_type = int(line[7:9], 16)
            length = int(line[1:3], 16)
            address = int(line[3:7], 16)
            
            if record_type in record_types:
                record_types[record_type] += 1
            else:
                record_types[record_type] = 1
            
            if record_type == 0x00:  # Data Record
                total_data_bytes += length
                address_ranges.append((address, address + length - 1))
        
        except Exception as e:
            print(f"Fehler in Zeile {line_num}: {e}")
    
    print(f"Gesamt-Zeilen: {len(lines)}")
    print(f"Daten-Bytes: {total_data_bytes}")
    print("Record-Typen:")
    for rt, count in record_types.items():
        rt_name = {0x00: "Data", 0x01: "EOF", 0x02: "Ext.Segment", 0x04: "Ext.Linear"}.get(rt, f"Unknown({rt:02X})")
        print(f"  {rt_name}: {count}")
    
    if address_ranges:
        min_addr = min(addr[0] for addr in address_ranges)
        max_addr = max(addr[1] for addr in address_ranges)
        print(f"Adress-Bereich: 0x{min_addr:04X} - 0x{max_addr:04X}")


def test_binary_output(data, filename="test_output.bin"):
    """Testet die Binär-Ausgabe"""
    print(f"\n=== Binär-Output Test ({filename}) ===")
    
    if not data:
        print("✗ Keine Daten zum Schreiben")
        return False
    
    try:
        # Als Binär-Datei speichern
        with open(filename, 'wb') as f:
            f.write(bytes(data))
        
        # Datei-Info
        file_size = Path(filename).stat().st_size
        print(f"✓ Binär-Datei erstellt: {filename}")
        print(f"  Größe: {file_size} Bytes")
        
        # Hex-Dump der ersten 64 Bytes
        print("  Erste 64 Bytes (Hex):")
        for i in range(0, min(64, len(data)), 16):
            line_data = data[i:i+16]
            hex_str = ' '.join(f'{b:02X}' for b in line_data)
            print(f"    {i:04X}: {hex_str}")
        
        return True
        
    except Exception as e:
        print(f"✗ Fehler beim Schreiben: {e}")
        return False


def test_sigma_studio_workflow():
    """Simuliert den kompletten Sigma Studio Workflow"""
    print("\n=== Sigma Studio Workflow Simulation ===")
    
    # Schritt 1: HEX-Datei "aus Sigma Studio"
    hex_content = create_sigma_studio_test_hex()
    
    # Schritt 2: HEX-Analyse
    analyze_hex_structure(hex_content)
    
    # Schritt 3: Konvertierung
    parser = SimpleHexParser()
    eeprom_data = parser.parse_hex_file(hex_content)
    
    # Schritt 4: Binär-Output
    success = test_binary_output(eeprom_data, "sigma_eeprom.bin")
    
    # Schritt 5: Verifikation
    if success:
        print("\n✓ Workflow erfolgreich!")
        print("  1. HEX-Datei gelesen ✓")
        print("  2. Struktur analysiert ✓") 
        print("  3. Zu Binär konvertiert ✓")
        print("  4. EEPROM-Datei erstellt ✓")
        
        # Kommando für ESP32-Programmierung
        print(f"\nNächster Schritt - ESP32 Programmierung:")
        print(f"python eeprom_programmer.py --hex-file sigma_studio_test.hex --host <ESP32_IP>")
        
        return True
    else:
        print("\n✗ Workflow fehlgeschlagen!")
        return False


def create_test_commands():
    """Erstellt Test-Befehle für manuelle Verifikation"""
    print("\n=== Test-Befehle für manuelle Verifikation ===")
    
    commands = [
        "# 1. Lokale Tests ausführen",
        "python test_hex_conversion.py",
        "",
        "# 2. ESP32 Verbindung testen", 
        "python eeprom_programmer.py --test --host *************",
        "",
        "# 3. Test-HEX programmieren",
        "python eeprom_programmer.py --hex-file sigma_studio_test.hex --host *************",
        "",
        "# 4. Mit DSP-Reset",
        "python eeprom_programmer.py --hex-file sigma_studio_test.hex --host ************* --reset-dsp",
        "",
        "# 5. EEPROM-Inhalt prüfen (manuell)",
        "# Erstellen Sie eeprom_dump.py und führen Sie aus:",
        "# python eeprom_dump.py --host ************* --address 0x0000 --length 256"
    ]
    
    with open("test_commands.txt", 'w') as f:
        f.write('\n'.join(commands))
    
    print("✓ Test-Befehle gespeichert in: test_commands.txt")
    
    for cmd in commands:
        print(f"  {cmd}")


def main():
    """Hauptfunktion für HEX-Konvertierungs-Tests"""
    print("HEX-zu-BIN Konvertierung - Detaillierte Tests")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # Test 1: Parser-Vergleich
    try:
        data1, data2 = test_hex_parser_comparison()
        if data1 and data2:
            success_count += 1
    except Exception as e:
        print(f"Test 1 Fehler: {e}")
    
    # Test 2: Struktur-Analyse
    try:
        hex_content = create_sigma_studio_test_hex()
        analyze_hex_structure(hex_content)
        success_count += 1
    except Exception as e:
        print(f"Test 2 Fehler: {e}")
    
    # Test 3: Binär-Output
    try:
        if 'data2' in locals() and data2:
            if test_binary_output(data2):
                success_count += 1
    except Exception as e:
        print(f"Test 3 Fehler: {e}")
    
    # Test 4: Kompletter Workflow
    try:
        if test_sigma_studio_workflow():
            success_count += 1
    except Exception as e:
        print(f"Test 4 Fehler: {e}")
    
    # Test-Befehle erstellen
    create_test_commands()
    
    # Ergebnis
    print(f"\n{'='*60}")
    print(f"HEX-Konvertierungs-Tests: {success_count}/{total_tests} erfolgreich")
    
    if success_count == total_tests:
        print("✅ Alle HEX-Konvertierungs-Tests bestanden!")
        print("\nDateien erstellt:")
        print("  - sigma_studio_test.hex (Test-HEX-Datei)")
        print("  - sigma_eeprom.bin (Konvertierte Binär-Datei)")
        print("  - test_commands.txt (Nächste Test-Schritte)")
        
        print("\n🚀 Bereit für ESP32-Tests!")
        return 0
    else:
        print("❌ Einige Tests fehlgeschlagen!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
