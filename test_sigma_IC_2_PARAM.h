/*
 * File:           I:\Coden\Audio\EspSigmatTcpiAdapter\test_sigma_IC_2_PARAM.h
 *
 * Created:        Saturday, June 14, 2025 12:18:48 AM
 * Description:    Dk2x2_220525spielen:IC 2 parameter RAM definitions.
 *
 * This software is distributed in the hope that it will be useful,
 * but is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 *
 * This software may only be used to program products purchased from
 * Analog Devices for incorporation by you into audio products that
 * are intended for resale to audio product end users. This software
 * may not be distributed whole or in any part to third parties.
 *
 * Copyright ©2025 Analog Devices, Inc. All rights reserved.
 */
#ifndef __TEST_SIGMA_IC_2_PARAM_H__
#define __TEST_SIGMA_IC_2_PARAM_H__


/* Module DC1 - DC Input Entry*/
#define MOD_DC1_COUNT                                  1
#define MOD_DC1_DEVICE                                 "IC2"
#define MOD_DC1_DCINPALG1_ADDR                         0
#define MOD_DC1_DCINPALG1_FIXPT                        0x00000003
#define MOD_DC1_DCINPALG1_VALUE                        SIGMASTUDIOTYPE_INTEGER_CONVERT(3)
#define MOD_DC1_DCINPALG1_TYPE                         SIGMASTUDIOTYPE_INTEGER

/* Module Compressor1 - Peak (gain)*/
#define MOD_COMPRESSOR1_COUNT                          36
#define MOD_COMPRESSOR1_DEVICE                         "IC2"
#define MOD_COMPRESSOR1_ALG0_GAINTABLE0_ADDR           1
#define MOD_COMPRESSOR1_ALG0_GAINTABLE0_FIXPT          0x00800000
#define MOD_COMPRESSOR1_ALG0_GAINTABLE0_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE0_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE1_ADDR           2
#define MOD_COMPRESSOR1_ALG0_GAINTABLE1_FIXPT          0x00800000
#define MOD_COMPRESSOR1_ALG0_GAINTABLE1_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE1_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE2_ADDR           3
#define MOD_COMPRESSOR1_ALG0_GAINTABLE2_FIXPT          0x00800000
#define MOD_COMPRESSOR1_ALG0_GAINTABLE2_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE2_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE3_ADDR           4
#define MOD_COMPRESSOR1_ALG0_GAINTABLE3_FIXPT          0x00800000
#define MOD_COMPRESSOR1_ALG0_GAINTABLE3_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE3_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE4_ADDR           5
#define MOD_COMPRESSOR1_ALG0_GAINTABLE4_FIXPT          0x00800000
#define MOD_COMPRESSOR1_ALG0_GAINTABLE4_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE4_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE5_ADDR           6
#define MOD_COMPRESSOR1_ALG0_GAINTABLE5_FIXPT          0x00800000
#define MOD_COMPRESSOR1_ALG0_GAINTABLE5_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE5_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE6_ADDR           7
#define MOD_COMPRESSOR1_ALG0_GAINTABLE6_FIXPT          0x00800000
#define MOD_COMPRESSOR1_ALG0_GAINTABLE6_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE6_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE7_ADDR           8
#define MOD_COMPRESSOR1_ALG0_GAINTABLE7_FIXPT          0x00800000
#define MOD_COMPRESSOR1_ALG0_GAINTABLE7_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE7_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE8_ADDR           9
#define MOD_COMPRESSOR1_ALG0_GAINTABLE8_FIXPT          0x00800000
#define MOD_COMPRESSOR1_ALG0_GAINTABLE8_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE8_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE9_ADDR           10
#define MOD_COMPRESSOR1_ALG0_GAINTABLE9_FIXPT          0x00800000
#define MOD_COMPRESSOR1_ALG0_GAINTABLE9_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE9_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE10_ADDR          11
#define MOD_COMPRESSOR1_ALG0_GAINTABLE10_FIXPT         0x00800000
#define MOD_COMPRESSOR1_ALG0_GAINTABLE10_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE10_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE11_ADDR          12
#define MOD_COMPRESSOR1_ALG0_GAINTABLE11_FIXPT         0x00800000
#define MOD_COMPRESSOR1_ALG0_GAINTABLE11_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE11_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE12_ADDR          13
#define MOD_COMPRESSOR1_ALG0_GAINTABLE12_FIXPT         0x00800000
#define MOD_COMPRESSOR1_ALG0_GAINTABLE12_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE12_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE13_ADDR          14
#define MOD_COMPRESSOR1_ALG0_GAINTABLE13_FIXPT         0x00800000
#define MOD_COMPRESSOR1_ALG0_GAINTABLE13_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE13_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE14_ADDR          15
#define MOD_COMPRESSOR1_ALG0_GAINTABLE14_FIXPT         0x00800000
#define MOD_COMPRESSOR1_ALG0_GAINTABLE14_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE14_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE15_ADDR          16
#define MOD_COMPRESSOR1_ALG0_GAINTABLE15_FIXPT         0x007FF6EC
#define MOD_COMPRESSOR1_ALG0_GAINTABLE15_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.999723034497089)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE15_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE16_ADDR          17
#define MOD_COMPRESSOR1_ALG0_GAINTABLE16_FIXPT         0x007D952C
#define MOD_COMPRESSOR1_ALG0_GAINTABLE16_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.981114959804912)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE16_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE17_ADDR          18
#define MOD_COMPRESSOR1_ALG0_GAINTABLE17_FIXPT         0x007B3609
#define MOD_COMPRESSOR1_ALG0_GAINTABLE17_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.962586564352992)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE17_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE18_ADDR          19
#define MOD_COMPRESSOR1_ALG0_GAINTABLE18_FIXPT         0x0078E25D
#define MOD_COMPRESSOR1_ALG0_GAINTABLE18_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.944408078393934)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE18_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE19_ADDR          20
#define MOD_COMPRESSOR1_ALG0_GAINTABLE19_FIXPT         0x007699F0
#define MOD_COMPRESSOR1_ALG0_GAINTABLE19_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.926572893872897)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE19_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE20_ADDR          21
#define MOD_COMPRESSOR1_ALG0_GAINTABLE20_FIXPT         0x00745C8D
#define MOD_COMPRESSOR1_ALG0_GAINTABLE20_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.909074527528428)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE20_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE21_ADDR          22
#define MOD_COMPRESSOR1_ALG0_GAINTABLE21_FIXPT         0x007229FE
#define MOD_COMPRESSOR1_ALG0_GAINTABLE21_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.891906618535722)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE21_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE22_ADDR          23
#define MOD_COMPRESSOR1_ALG0_GAINTABLE22_FIXPT         0x0070020F
#define MOD_COMPRESSOR1_ALG0_GAINTABLE22_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.875062926194409)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE22_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE23_ADDR          24
#define MOD_COMPRESSOR1_ALG0_GAINTABLE23_FIXPT         0x0068E32E
#define MOD_COMPRESSOR1_ALG0_GAINTABLE23_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.819433037601235)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE23_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE24_ADDR          25
#define MOD_COMPRESSOR1_ALG0_GAINTABLE24_FIXPT         0x004A412E
#define MOD_COMPRESSOR1_ALG0_GAINTABLE24_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.580114164554883)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE24_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE25_ADDR          26
#define MOD_COMPRESSOR1_ALG0_GAINTABLE25_FIXPT         0x00349178
#define MOD_COMPRESSOR1_ALG0_GAINTABLE25_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.410689377258156)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE25_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE26_ADDR          27
#define MOD_COMPRESSOR1_ALG0_GAINTABLE26_FIXPT         0x00253728
#define MOD_COMPRESSOR1_ALG0_GAINTABLE26_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.290745813321258)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE26_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE27_ADDR          28
#define MOD_COMPRESSOR1_ALG0_GAINTABLE27_FIXPT         0x001A58B6
#define MOD_COMPRESSOR1_ALG0_GAINTABLE27_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.205832272868122)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE27_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE28_ADDR          29
#define MOD_COMPRESSOR1_ALG0_GAINTABLE28_FIXPT         0x0012A6E3
#define MOD_COMPRESSOR1_ALG0_GAINTABLE28_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.145718089867193)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE28_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE29_ADDR          30
#define MOD_COMPRESSOR1_ALG0_GAINTABLE29_FIXPT         0x000D345D
#define MOD_COMPRESSOR1_ALG0_GAINTABLE29_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.103160507429988)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE29_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE30_ADDR          31
#define MOD_COMPRESSOR1_ALG0_GAINTABLE30_FIXPT         0x0009591D
#define MOD_COMPRESSOR1_ALG0_GAINTABLE30_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.0730320463499886)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE30_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE31_ADDR          32
#define MOD_COMPRESSOR1_ALG0_GAINTABLE31_FIXPT         0x00069E31
#define MOD_COMPRESSOR1_ALG0_GAINTABLE31_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.0517027293384214)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE31_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_GAINTABLE32_ADDR          33
#define MOD_COMPRESSOR1_ALG0_GAINTABLE32_FIXPT         0x0004AF65
#define MOD_COMPRESSOR1_ALG0_GAINTABLE32_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.0366027292762895)
#define MOD_COMPRESSOR1_ALG0_GAINTABLE32_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_POSTGAIN_ADDR             34
#define MOD_COMPRESSOR1_ALG0_POSTGAIN_FIXPT            0x009820D7
#define MOD_COMPRESSOR1_ALG0_POSTGAIN_VALUE            SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.18850222743702)
#define MOD_COMPRESSOR1_ALG0_POSTGAIN_TYPE             SIGMASTUDIOTYPE_FIXPOINT
#define MOD_COMPRESSOR1_ALG0_HOLD_ADDR                 35
#define MOD_COMPRESSOR1_ALG0_HOLD_FIXPT                0x000002D0
#define MOD_COMPRESSOR1_ALG0_HOLD_VALUE                SIGMASTUDIOTYPE_INTEGER_CONVERT(720)
#define MOD_COMPRESSOR1_ALG0_HOLD_TYPE                 SIGMASTUDIOTYPE_INTEGER
#define MOD_COMPRESSOR1_ALG0_DECAY_ADDR                36
#define MOD_COMPRESSOR1_ALG0_DECAY_FIXPT               0x00000091
#define MOD_COMPRESSOR1_ALG0_DECAY_VALUE               SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.73611111111111E-05)
#define MOD_COMPRESSOR1_ALG0_DECAY_TYPE                SIGMASTUDIOTYPE_FIXPOINT

/* Module SW vol 1_4 - Single slew ext vol*/
#define MOD_SWVOL1_4_COUNT                             1
#define MOD_SWVOL1_4_DEVICE                            "IC2"
#define MOD_SWVOL1_4_EXTSWGAINDB1STEP_ADDR             37
#define MOD_SWVOL1_4_EXTSWGAINDB1STEP_FIXPT            0x00000800
#define MOD_SWVOL1_4_EXTSWGAINDB1STEP_VALUE            SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.000244140625)
#define MOD_SWVOL1_4_EXTSWGAINDB1STEP_TYPE             SIGMASTUDIOTYPE_FIXPOINT

/* Module SW vol 1 - Single SW slew vol (adjustable)*/
#define MOD_SWVOL1_COUNT                               2
#define MOD_SWVOL1_DEVICE                              "IC2"
#define MOD_SWVOL1_ALG0_TARGET_ADDR                    38
#define MOD_SWVOL1_ALG0_TARGET_FIXPT                   0x00F3E623
#define MOD_SWVOL1_ALG0_TARGET_VALUE                   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.90546071796325)
#define MOD_SWVOL1_ALG0_TARGET_TYPE                    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_SWVOL1_ALG0_STEP_ADDR                      39
#define MOD_SWVOL1_ALG0_STEP_FIXPT                     0x00000800
#define MOD_SWVOL1_ALG0_STEP_VALUE                     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.000244140625)
#define MOD_SWVOL1_ALG0_STEP_TYPE                      SIGMASTUDIOTYPE_FIXPOINT

/* Module RMS Limiter Hi Res - Hi Resolution RMS*/
#define MOD_RMSLIMITERHIRES_COUNT                      70
#define MOD_RMSLIMITERHIRES_DEVICE                     "IC2"
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES10_ADDR 40
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES10_VALUES SIGMASTUDIOTYPE_FIXPOINT(0x00000000000000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES10_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES11_ADDR 41
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES11_VALUES SIGMASTUDIOTYPE_FIXPOINT(0x00000000000000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000008000000080000000800000)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES11_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES12_ADDR 42
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES12_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES12_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES12_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES13_ADDR 43
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES13_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES13_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES13_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES14_ADDR 44
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES14_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES14_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES14_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES15_ADDR 45
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES15_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES15_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES15_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES16_ADDR 46
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES16_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES16_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES16_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES17_ADDR 47
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES17_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES17_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES17_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES18_ADDR 48
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES18_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES18_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES18_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES19_ADDR 49
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES19_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES19_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES19_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES110_ADDR 50
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES110_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES110_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES110_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES111_ADDR 51
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES111_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES111_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES111_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES112_ADDR 52
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES112_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES112_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES112_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES113_ADDR 53
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES113_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES113_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES113_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES114_ADDR 54
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES114_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES114_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES114_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES115_ADDR 55
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES115_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES115_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES115_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES116_ADDR 56
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES116_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES116_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES116_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES117_ADDR 57
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES117_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES117_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES117_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES118_ADDR 58
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES118_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES118_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES118_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES119_ADDR 59
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES119_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES119_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES119_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES120_ADDR 60
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES120_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES120_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES120_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES121_ADDR 61
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES121_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES121_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES121_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES122_ADDR 62
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES122_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES122_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES122_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES123_ADDR 63
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES123_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES123_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES123_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES124_ADDR 64
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES124_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES124_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES124_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES125_ADDR 65
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES125_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES125_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES125_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES126_ADDR 66
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES126_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES126_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES126_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES127_ADDR 67
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES127_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES127_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES127_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES128_ADDR 68
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES128_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES128_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES128_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES129_ADDR 69
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES129_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES129_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES129_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES130_ADDR 70
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES130_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES130_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES130_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES131_ADDR 71
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES131_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES131_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES131_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES132_ADDR 72
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES132_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES132_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES132_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES133_ADDR 73
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES133_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES133_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES133_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES134_ADDR 74
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES134_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES134_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES134_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES135_ADDR 75
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES135_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES135_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES135_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES136_ADDR 76
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES136_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES136_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES136_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES137_ADDR 77
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES137_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES137_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES137_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES138_ADDR 78
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES138_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES138_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES138_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES139_ADDR 79
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES139_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES139_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES139_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES140_ADDR 80
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES140_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES140_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES140_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES141_ADDR 81
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES141_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES141_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES141_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES142_ADDR 82
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES142_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES142_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES142_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES143_ADDR 83
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES143_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES143_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES143_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES144_ADDR 84
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES144_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES144_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES144_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES145_ADDR 85
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES145_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES145_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES145_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES146_ADDR 86
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES146_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES146_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES146_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES147_ADDR 87
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES147_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES147_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES147_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES148_ADDR 88
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES148_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES148_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES148_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES149_ADDR 89
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES149_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES149_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES149_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES150_ADDR 90
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES150_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES150_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES150_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES151_ADDR 91
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES151_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES151_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES151_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES152_ADDR 92
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES152_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES152_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES152_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES153_ADDR 93
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES153_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES153_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES153_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES154_ADDR 94
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES154_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES154_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES154_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES155_ADDR 95
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES155_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES155_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES155_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES156_ADDR 96
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES156_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES156_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES156_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES157_ADDR 97
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES157_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES157_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES157_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES158_ADDR 98
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES158_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES158_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES158_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES159_ADDR 99
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES159_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES159_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES159_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES160_ADDR 100
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES160_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES160_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES160_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES161_ADDR 101
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES161_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES161_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES161_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES162_ADDR 102
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES162_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES162_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES162_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES163_ADDR 103
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES163_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES163_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES163_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES164_ADDR 104
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES164_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES164_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES164_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES165_ADDR 105
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES165_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES165_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES165_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES166_ADDR 106
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES166_FIXPT 0x00800000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES166_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES166_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES1RMS_ADDR 107
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES1RMS_FIXPT 0x00001306
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES1RMS_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.000580611815350629)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES1RMS_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES1HOLD_ADDR 108
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES1HOLD_FIXPT 0x00000000
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES1HOLD_VALUE SIGMASTUDIOTYPE_INTEGER_CONVERT(0)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES1HOLD_TYPE SIGMASTUDIOTYPE_INTEGER
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES1DECAY_ADDR 109
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES1DECAY_FIXPT 0x00000012
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES1DECAY_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(2.17013888888889E-06)
#define MOD_RMSLIMITERHIRES_ALG0_TWOCHANNELSINGLEDETECTALGHIRES1DECAY_TYPE SIGMASTUDIOTYPE_FIXPOINT

/* Module Auto EQ1_2 - Automatic Speaker EQ*/
#define MOD_AUTOEQ1_2_COUNT                            33
#define MOD_AUTOEQ1_2_DEVICE                           "IC2"
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT1_PARAMB0_ADDR    110
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT1_PARAMB0_FIXPT   0x00800000
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT1_PARAMB0_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT1_PARAMB0_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT1_PARAMB1_ADDR    111
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT1_PARAMB1_FIXPT   0x00000000
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT1_PARAMB1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT1_PARAMB1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT1_PARAMA1_ADDR    112
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT1_PARAMA1_FIXPT   0x00000000
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT1_PARAMA1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT1_PARAMA1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMB0_ADDR    113
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMB0_FIXPT   0x00760911
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMB0_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.922151742769656)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMB0_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMB1_ADDR    114
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMB1_FIXPT   0xFF3840AC
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMB1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.56052647631246)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMB1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMA1_ADDR    115
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMA1_FIXPT   0x00C7BF54
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMA1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.56052647631246)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMA1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMB2_ADDR    116
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMB2_FIXPT   0x00644117
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMB2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.78323639575048)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMB2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMA2_ADDR    117
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMA2_FIXPT   0xFFA5B5D8
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMA2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.705388138520136)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT2_PARAMA2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMB0_ADDR    118
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMB0_FIXPT   0x00A60979
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMB0_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.29716415128151)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMB0_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMB1_ADDR    119
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMB1_FIXPT   0x003705AB
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMB1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.429860533015883)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMB1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMA1_ADDR    120
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMA1_FIXPT   0xFFC8FA55
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMA1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.429860533015883)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMA1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMB2_ADDR    121
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMB2_FIXPT   0x0016169C
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMB2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.172565003029725)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMB2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMA2_ADDR    122
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMA2_FIXPT   0xFFC3DFEB
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMA2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.469729154311238)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT3_PARAMA2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMB0_ADDR    123
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMB0_FIXPT   0x007DB73E
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMB0_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.982154611363082)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMB0_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMB1_ADDR    124
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMB1_FIXPT   0xFF07B3D5
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMB1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.93982451301707)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMB1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMA1_ADDR    125
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMA1_FIXPT   0x00F84C2B
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMA1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.93982451301707)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMA1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMB2_ADDR    126
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMB2_FIXPT   0x007B6A2D
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMB2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.96417783603839)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMB2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMA2_ADDR    127
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMA2_FIXPT   0xFF86DE95
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMA2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.946332447401471)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT4_PARAMA2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMB0_ADDR    128
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMB0_FIXPT   0x00806715
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMB0_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.00314591998666)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMB0_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMB1_ADDR    129
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMB1_FIXPT   0xFF041FCB
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMB1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.96777986220277)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMB1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMA1_ADDR    130
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMA1_FIXPT   0x00FBE035
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMA1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.96777986220277)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMA1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMB2_ADDR    131
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMB2_FIXPT   0x007B8F78
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMB2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.96531592487199)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMB2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMA2_ADDR    132
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMA2_FIXPT   0xFF840972
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMA2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.968461844858648)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT5_PARAMA2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMB0_ADDR    133
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMB0_FIXPT   0x00802565
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMB0_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.00114119539476)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMB0_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMB1_ADDR    134
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMB1_FIXPT   0xFF0126C6
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMB1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.99100424010813)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMB1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMA1_ADDR    135
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMA1_FIXPT   0x00FED93A
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMA1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.99100424010813)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMA1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMB2_ADDR    136
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMB2_FIXPT   0x007EB67E
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMB2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.989944258992727)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMB2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMA2_ADDR    137
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMA2_FIXPT   0xFF81241D
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMA2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.99108545438749)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT6_PARAMA2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMB0_ADDR    138
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMB0_FIXPT   0x007B2DD7
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMB0_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.9623364786929)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMB0_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMB1_ADDR    139
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMB1_FIXPT   0xFF7339B9
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMB1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.09980101195183)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMB1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMA1_ADDR    140
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMA1_FIXPT   0x008CC647
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMA1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.09980101195183)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMA1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMB2_ADDR    141
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMB2_FIXPT   0x0066D684
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMB2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.803421530748194)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMB2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMA2_ADDR    142
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMA2_FIXPT   0xFF9DFBA5
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMA2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.765758009441094)
#define MOD_AUTOEQ1_2_ALG0_BAND0_FILT7_PARAMA2_TYPE    SIGMASTUDIOTYPE_FIXPOINT

/* Module Auto EQ1 - Automatic Speaker EQ*/
#define MOD_AUTOEQ1_COUNT                              33
#define MOD_AUTOEQ1_DEVICE                             "IC2"
#define MOD_AUTOEQ1_ALG0_BAND0_FILT1_PARAMB0_ADDR      143
#define MOD_AUTOEQ1_ALG0_BAND0_FILT1_PARAMB0_FIXPT     0x00800000
#define MOD_AUTOEQ1_ALG0_BAND0_FILT1_PARAMB0_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT1_PARAMB0_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT1_PARAMB1_ADDR      144
#define MOD_AUTOEQ1_ALG0_BAND0_FILT1_PARAMB1_FIXPT     0x00000000
#define MOD_AUTOEQ1_ALG0_BAND0_FILT1_PARAMB1_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT1_PARAMB1_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT1_PARAMA1_ADDR      145
#define MOD_AUTOEQ1_ALG0_BAND0_FILT1_PARAMA1_FIXPT     0x00000000
#define MOD_AUTOEQ1_ALG0_BAND0_FILT1_PARAMA1_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT1_PARAMA1_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMB0_ADDR      146
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMB0_FIXPT     0x007804B7
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMB0_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.937643900353112)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMB0_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMB1_ADDR      147
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMB1_FIXPT     0xFF37015C
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMB1_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.57027109314371)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMB1_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMA1_ADDR      148
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMA1_FIXPT     0x00C8FEA4
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMA1_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.57027109314371)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMA1_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMB2_ADDR      149
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMB2_FIXPT     0x00644AED
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMB2_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.783536654779012)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMB2_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMA2_ADDR      150
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMA2_FIXPT     0xFFA3B05C
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMA2_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.721180555132124)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT2_PARAMA2_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMB0_ADDR      151
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMB0_FIXPT     0x00939BD8
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMB0_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.15319354286411)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMB0_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMB1_ADDR      152
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMB1_FIXPT     0x002C949D
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMB1_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.348285349514937)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMB1_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMA1_ADDR      153
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMA1_FIXPT     0xFFD36B63
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMA1_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.348285349514937)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMA1_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMB2_ADDR      154
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMB2_FIXPT     0x001EFAA4
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMB2_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.242023961632182)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMB2_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMA2_ADDR      155
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMA2_FIXPT     0xFFCD6984
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMA2_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.395217504496296)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT3_PARAMA2_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMB0_ADDR      156
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMB0_FIXPT     0x007E707B
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMB0_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.98780774318042)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMB0_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMB1_ADDR      157
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMB1_FIXPT     0xFF06C4FE
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMB1_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.94711329738848)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMB1_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMA1_ADDR      158
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMA1_FIXPT     0x00F93B02
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMA1_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.94711329738848)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMA1_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMB2_ADDR      159
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMB2_FIXPT     0x007BA792
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMB2_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.966051423292263)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMB2_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMA2_ADDR      160
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMA2_FIXPT     0xFF85E7F2
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMA2_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.953859166472683)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT4_PARAMA2_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMB0_ADDR      161
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMB0_FIXPT     0x00806715
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMB0_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.00314591998666)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMB0_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMB1_ADDR      162
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMB1_FIXPT     0xFF041FCB
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMB1_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.96777986220277)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMB1_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMA1_ADDR      163
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMA1_FIXPT     0x00FBE035
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMA1_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.96777986220277)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMA1_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMB2_ADDR      164
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMB2_FIXPT     0x007B8F78
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMB2_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.96531592487199)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMB2_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMA2_ADDR      165
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMA2_FIXPT     0xFF840972
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMA2_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.968461844858648)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT5_PARAMA2_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMB0_ADDR      166
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMB0_FIXPT     0x00802565
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMB0_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.00114119539476)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMB0_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMB1_ADDR      167
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMB1_FIXPT     0xFF0126C6
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMB1_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.99100424010813)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMB1_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMA1_ADDR      168
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMA1_FIXPT     0x00FED93A
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMA1_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.99100424010813)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMA1_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMB2_ADDR      169
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMB2_FIXPT     0x007EB67E
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMB2_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.989944258992727)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMB2_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMA2_ADDR      170
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMA2_FIXPT     0xFF81241D
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMA2_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.99108545438749)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT6_PARAMA2_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMB0_ADDR      171
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMB0_FIXPT     0x007C99EE
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMB0_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.9734476567034)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMB0_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMB1_ADDR      172
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMB1_FIXPT     0xFF70EFD1
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMB1_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.11768141740419)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMB1_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMA1_ADDR      173
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMA1_FIXPT     0x008F102F
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMA1_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.11768141740419)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMA1_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMB2_ADDR      174
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMB2_FIXPT     0x006709A3
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMB2_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.804981593215055)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMB2_TYPE      SIGMASTUDIOTYPE_FIXPOINT
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMA2_ADDR      175
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMA2_FIXPT     0xFF9C5C6F
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMA2_VALUE     SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.778429249918454)
#define MOD_AUTOEQ1_ALG0_BAND0_FILT7_PARAMA2_TYPE      SIGMASTUDIOTYPE_FIXPOINT

/* Module 2nd Order Filter1 - General (2nd Order/Lookup)*/
#define MOD_2NDORDERFILTER1_COUNT                      21
#define MOD_2NDORDERFILTER1_DEVICE                     "IC2"
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX1FIVE_ADDR 176
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX1FIVE_FIXPT 0x02800000
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX1FIVE_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(5)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX1FIVE_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B00_ADDR 177
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B00_FIXPT 0x00475107
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B00_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.55716030470079)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B00_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B01_ADDR 178
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B01_FIXPT 0xFF715DF2
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B01_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.11432060940158)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B01_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B02_ADDR 179
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B02_FIXPT 0x00475107
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B02_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.55716030470079)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B02_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B03_ADDR 180
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B03_FIXPT 0x00FDA16A
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B03_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.98148850914457)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B03_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B04_ADDR 181
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B04_FIXPT 0xFF825906
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B04_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.981658282617134)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B04_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B05_ADDR 182
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B05_FIXPT 0x00477D8F
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B05_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.558519270445063)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B05_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B06_ADDR 183
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B06_FIXPT 0xFF7104E2
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B06_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.11703854089013)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B06_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B07_ADDR 184
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B07_FIXPT 0x00477D8F
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B07_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.558519270445063)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B07_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B08_ADDR 185
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B08_FIXPT 0x00FE410F
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B08_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.98636044207521)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B08_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B09_ADDR 186
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B09_FIXPT 0xFF81BBEA
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B09_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.986452832894453)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B09_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B010_ADDR 187
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B010_FIXPT 0x00479E70
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B010_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.559522685212452)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B010_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B011_ADDR 188
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B011_FIXPT 0xFF70C320
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B011_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.1190453704249)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B011_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B012_ADDR 189
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B012_FIXPT 0x00479E70
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B012_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.559522685212452)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B012_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B013_ADDR 190
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B013_FIXPT 0x00FEB6B0
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B013_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.9899502175218)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B013_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B014_ADDR 191
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B014_FIXPT 0xFF8147AB
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B014_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.99000046473017)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B014_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B015_ADDR 192
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B015_FIXPT 0x0047B6B4
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B015_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.560263160752416)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B015_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B016_ADDR 193
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B016_FIXPT 0xFF709298
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B016_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.12052632150483)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B016_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B017_ADDR 194
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B017_FIXPT 0x0047B6B4
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B017_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.560263160752416)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B017_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B018_ADDR 195
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B018_FIXPT 0x00FF0D5C
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B018_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.9925952287503)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B018_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B019_ADDR 196
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B019_FIXPT 0xFF80F1BF
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B019_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.992622543127095)
#define MOD_2NDORDERFILTER1_ALG0_PARAMTONEINDEXALGFIX10B019_TYPE SIGMASTUDIOTYPE_FIXPOINT

/* Module Crossover1 - Crossover*/
#define MOD_CROSSOVER1_COUNT                           84
#define MOD_CROSSOVER1_DEVICE                          "IC2"
#define MOD_CROSSOVER1_ALG0_CROSSOVERFILTER2WAYALGDP1LOWINVERT_ADDR 197
#define MOD_CROSSOVER1_ALG0_CROSSOVERFILTER2WAYALGDP1LOWINVERT_FIXPT 0x00800000
#define MOD_CROSSOVER1_ALG0_CROSSOVERFILTER2WAYALGDP1LOWINVERT_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_CROSSOVER1_ALG0_CROSSOVERFILTER2WAYALGDP1LOWINVERT_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_CROSSOVERFILTER2WAYALGDP1LOWINVERT_ADDR 197
#define MOD_CROSSOVER1_ALG0_CROSSOVERFILTER2WAYALGDP1LOWINVERT_FIXPT 0x00800000
#define MOD_CROSSOVER1_ALG0_CROSSOVERFILTER2WAYALGDP1LOWINVERT_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_CROSSOVER1_ALG0_CROSSOVERFILTER2WAYALGDP1LOWINVERT_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB0_ADDR     198
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB0_FIXPT    0x00000258
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB0_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(7.15317998432368E-05)
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB0_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB0_ADDR     198
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB0_FIXPT    0x00000258
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB0_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(7.15317998432368E-05)
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB0_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB1_ADDR     199
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB1_FIXPT    0x000004B0
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB1_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.000143063599686474)
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB1_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB1_ADDR     199
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB1_FIXPT    0x000004B0
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB1_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.000143063599686474)
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB1_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMA1_ADDR     200
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMA1_FIXPT    0x00FCEB74
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMA1_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.97593552482925)
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMA1_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMA1_ADDR     200
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMA1_FIXPT    0x00FCEB74
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMA1_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.97593552482925)
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMA1_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB2_ADDR     201
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB2_FIXPT    0x00000258
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB2_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(7.15317998432368E-05)
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB2_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB2_ADDR     201
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB2_FIXPT    0x00000258
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB2_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(7.15317998432368E-05)
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMB2_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMA2_ADDR     202
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMA2_FIXPT    0xFF830B2C
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMA2_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.97622165202862)
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMA2_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMA2_ADDR     202
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMA2_FIXPT    0xFF830B2C
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMA2_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.97622165202862)
#define MOD_CROSSOVER1_ALG0_LOW_FILT1_PARAMA2_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB0_ADDR     203
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB0_FIXPT    0x00000258
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB0_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(7.15317998432368E-05)
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB0_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB0_ADDR     203
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB0_FIXPT    0x00000258
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB0_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(7.15317998432368E-05)
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB0_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB1_ADDR     204
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB1_FIXPT    0x000004B0
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB1_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.000143063599686474)
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB1_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB1_ADDR     204
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB1_FIXPT    0x000004B0
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB1_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.000143063599686474)
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB1_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMA1_ADDR     205
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMA1_FIXPT    0x00FCEB74
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMA1_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.97593552482925)
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMA1_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMA1_ADDR     205
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMA1_FIXPT    0x00FCEB74
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMA1_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.97593552482925)
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMA1_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB2_ADDR     206
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB2_FIXPT    0x00000258
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB2_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(7.15317998432368E-05)
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB2_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB2_ADDR     206
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB2_FIXPT    0x00000258
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB2_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(7.15317998432368E-05)
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMB2_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMA2_ADDR     207
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMA2_FIXPT    0xFF830B2C
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMA2_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.97622165202862)
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMA2_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMA2_ADDR     207
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMA2_FIXPT    0xFF830B2C
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMA2_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.97622165202862)
#define MOD_CROSSOVER1_ALG0_LOW_FILT2_PARAMA2_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB0_ADDR    208
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB0_FIXPT   0x007E7812
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB0_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.988039294214467)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB0_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB0_ADDR    208
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB0_FIXPT   0x007E7812
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB0_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.988039294214467)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB0_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB1_ADDR    209
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB1_FIXPT   0xFF030FDC
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.97607858842893)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB1_ADDR    209
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB1_FIXPT   0xFF030FDC
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.97607858842893)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMA1_ADDR    210
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMA1_FIXPT   0x00FCEB74
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMA1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.97593552482925)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMA1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMA1_ADDR    210
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMA1_FIXPT   0x00FCEB74
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMA1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.97593552482925)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMA1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB2_ADDR    211
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB2_FIXPT   0x007E7812
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.988039294214467)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB2_ADDR    211
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB2_FIXPT   0x007E7812
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.988039294214467)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMB2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMA2_ADDR    212
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMA2_FIXPT   0xFF830B2C
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMA2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.97622165202862)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMA2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMA2_ADDR    212
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMA2_FIXPT   0xFF830B2C
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMA2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.97622165202862)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT1_PARAMA2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB0_ADDR    213
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB0_FIXPT   0x007E7812
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB0_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.988039294214467)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB0_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB0_ADDR    213
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB0_FIXPT   0x007E7812
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB0_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.988039294214467)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB0_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB1_ADDR    214
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB1_FIXPT   0xFF030FDC
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.97607858842893)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB1_ADDR    214
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB1_FIXPT   0xFF030FDC
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.97607858842893)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMA1_ADDR    215
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMA1_FIXPT   0x00FCEB74
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMA1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.97593552482925)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMA1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMA1_ADDR    215
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMA1_FIXPT   0x00FCEB74
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMA1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.97593552482925)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMA1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB2_ADDR    216
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB2_FIXPT   0x007E7812
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.988039294214467)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB2_ADDR    216
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB2_FIXPT   0x007E7812
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.988039294214467)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMB2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMA2_ADDR    217
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMA2_FIXPT   0xFF830B2C
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMA2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.97622165202862)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMA2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMA2_ADDR    217
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMA2_FIXPT   0xFF830B2C
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMA2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.97622165202862)
#define MOD_CROSSOVER1_ALG0_HIGH_FILT2_PARAMA2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_CROSSOVERFILTER2WAYALGDP2LOWINVERT_ADDR 218
#define MOD_CROSSOVER1_ALG1_CROSSOVERFILTER2WAYALGDP2LOWINVERT_FIXPT 0x00800000
#define MOD_CROSSOVER1_ALG1_CROSSOVERFILTER2WAYALGDP2LOWINVERT_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_CROSSOVER1_ALG1_CROSSOVERFILTER2WAYALGDP2LOWINVERT_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_CROSSOVERFILTER2WAYALGDP2LOWINVERT_ADDR 218
#define MOD_CROSSOVER1_ALG1_CROSSOVERFILTER2WAYALGDP2LOWINVERT_FIXPT 0x00800000
#define MOD_CROSSOVER1_ALG1_CROSSOVERFILTER2WAYALGDP2LOWINVERT_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_CROSSOVER1_ALG1_CROSSOVERFILTER2WAYALGDP2LOWINVERT_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB0_ADDR     219
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB0_FIXPT    0x00000258
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB0_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(7.15317998432368E-05)
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB0_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB0_ADDR     219
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB0_FIXPT    0x00000258
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB0_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(7.15317998432368E-05)
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB0_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB1_ADDR     220
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB1_FIXPT    0x000004B0
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB1_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.000143063599686474)
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB1_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB1_ADDR     220
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB1_FIXPT    0x000004B0
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB1_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.000143063599686474)
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB1_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMA1_ADDR     221
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMA1_FIXPT    0x00FCEB74
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMA1_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.97593552482925)
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMA1_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMA1_ADDR     221
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMA1_FIXPT    0x00FCEB74
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMA1_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.97593552482925)
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMA1_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB2_ADDR     222
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB2_FIXPT    0x00000258
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB2_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(7.15317998432368E-05)
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB2_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB2_ADDR     222
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB2_FIXPT    0x00000258
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB2_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(7.15317998432368E-05)
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMB2_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMA2_ADDR     223
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMA2_FIXPT    0xFF830B2C
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMA2_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.97622165202862)
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMA2_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMA2_ADDR     223
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMA2_FIXPT    0xFF830B2C
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMA2_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.97622165202862)
#define MOD_CROSSOVER1_ALG1_LOW_FILT1_PARAMA2_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB0_ADDR     224
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB0_FIXPT    0x00000258
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB0_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(7.15317998432368E-05)
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB0_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB0_ADDR     224
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB0_FIXPT    0x00000258
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB0_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(7.15317998432368E-05)
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB0_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB1_ADDR     225
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB1_FIXPT    0x000004B0
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB1_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.000143063599686474)
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB1_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB1_ADDR     225
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB1_FIXPT    0x000004B0
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB1_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.000143063599686474)
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB1_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMA1_ADDR     226
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMA1_FIXPT    0x00FCEB74
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMA1_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.97593552482925)
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMA1_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMA1_ADDR     226
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMA1_FIXPT    0x00FCEB74
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMA1_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.97593552482925)
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMA1_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB2_ADDR     227
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB2_FIXPT    0x00000258
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB2_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(7.15317998432368E-05)
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB2_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB2_ADDR     227
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB2_FIXPT    0x00000258
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB2_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(7.15317998432368E-05)
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMB2_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMA2_ADDR     228
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMA2_FIXPT    0xFF830B2C
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMA2_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.97622165202862)
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMA2_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMA2_ADDR     228
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMA2_FIXPT    0xFF830B2C
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMA2_VALUE    SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.97622165202862)
#define MOD_CROSSOVER1_ALG1_LOW_FILT2_PARAMA2_TYPE     SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB0_ADDR    229
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB0_FIXPT   0x007E7812
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB0_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.988039294214467)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB0_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB0_ADDR    229
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB0_FIXPT   0x007E7812
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB0_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.988039294214467)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB0_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB1_ADDR    230
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB1_FIXPT   0xFF030FDC
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.97607858842893)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB1_ADDR    230
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB1_FIXPT   0xFF030FDC
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.97607858842893)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMA1_ADDR    231
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMA1_FIXPT   0x00FCEB74
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMA1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.97593552482925)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMA1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMA1_ADDR    231
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMA1_FIXPT   0x00FCEB74
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMA1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.97593552482925)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMA1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB2_ADDR    232
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB2_FIXPT   0x007E7812
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.988039294214467)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB2_ADDR    232
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB2_FIXPT   0x007E7812
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.988039294214467)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMB2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMA2_ADDR    233
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMA2_FIXPT   0xFF830B2C
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMA2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.97622165202862)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMA2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMA2_ADDR    233
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMA2_FIXPT   0xFF830B2C
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMA2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.97622165202862)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT1_PARAMA2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB0_ADDR    234
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB0_FIXPT   0x007E7812
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB0_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.988039294214467)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB0_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB0_ADDR    234
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB0_FIXPT   0x007E7812
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB0_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.988039294214467)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB0_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB1_ADDR    235
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB1_FIXPT   0xFF030FDC
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.97607858842893)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB1_ADDR    235
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB1_FIXPT   0xFF030FDC
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.97607858842893)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMA1_ADDR    236
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMA1_FIXPT   0x00FCEB74
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMA1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.97593552482925)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMA1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMA1_ADDR    236
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMA1_FIXPT   0x00FCEB74
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMA1_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.97593552482925)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMA1_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB2_ADDR    237
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB2_FIXPT   0x007E7812
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.988039294214467)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB2_ADDR    237
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB2_FIXPT   0x007E7812
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.988039294214467)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMB2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMA2_ADDR    238
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMA2_FIXPT   0xFF830B2C
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMA2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.97622165202862)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMA2_TYPE    SIGMASTUDIOTYPE_FIXPOINT
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMA2_ADDR    238
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMA2_FIXPT   0xFF830B2C
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMA2_VALUE   SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.97622165202862)
#define MOD_CROSSOVER1_ALG1_HIGH_FILT2_PARAMA2_TYPE    SIGMASTUDIOTYPE_FIXPOINT

/* Module SW vol 1_3 - Single slew ext vol*/
#define MOD_SWVOL1_3_COUNT                             1
#define MOD_SWVOL1_3_DEVICE                            "IC2"
#define MOD_SWVOL1_3_EXTSWGAINDB4STEP_ADDR             239
#define MOD_SWVOL1_3_EXTSWGAINDB4STEP_FIXPT            0x00000800
#define MOD_SWVOL1_3_EXTSWGAINDB4STEP_VALUE            SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.000244140625)
#define MOD_SWVOL1_3_EXTSWGAINDB4STEP_TYPE             SIGMASTUDIOTYPE_FIXPOINT

/* Module SW vol 1_2 - Single slew ext vol*/
#define MOD_SWVOL1_2_COUNT                             1
#define MOD_SWVOL1_2_DEVICE                            "IC2"
#define MOD_SWVOL1_2_EXTSWGAINDB3STEP_ADDR             240
#define MOD_SWVOL1_2_EXTSWGAINDB3STEP_FIXPT            0x00000800
#define MOD_SWVOL1_2_EXTSWGAINDB3STEP_VALUE            SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.000244140625)
#define MOD_SWVOL1_2_EXTSWGAINDB3STEP_TYPE             SIGMASTUDIOTYPE_FIXPOINT

/* Module St Mixer1 - Stereo Mixer*/
#define MOD_STMIXER1_COUNT                             2
#define MOD_STMIXER1_DEVICE                            "IC2"
#define MOD_STMIXER1_ALG0_STAGE0_VOLUME_ADDR           241
#define MOD_STMIXER1_ALG0_STAGE0_VOLUME_FIXPT          0x00800000
#define MOD_STMIXER1_ALG0_STAGE0_VOLUME_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_STMIXER1_ALG0_STAGE0_VOLUME_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_STMIXER1_ALG0_STAGE1_VOLUME_ADDR           242
#define MOD_STMIXER1_ALG0_STAGE1_VOLUME_FIXPT          0x00800000
#define MOD_STMIXER1_ALG0_STAGE1_VOLUME_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_STMIXER1_ALG0_STAGE1_VOLUME_TYPE           SIGMASTUDIOTYPE_FIXPOINT

/* Module 2000 - Crossover*/
#define MOD_2000_COUNT                                 21
#define MOD_2000_DEVICE                                "IC2"
#define MOD_2000_ALG0_CROSSOVERFILTER2WAYALGDP3LOWINVERT_ADDR 243
#define MOD_2000_ALG0_CROSSOVERFILTER2WAYALGDP3LOWINVERT_FIXPT 0x00800000
#define MOD_2000_ALG0_CROSSOVERFILTER2WAYALGDP3LOWINVERT_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_2000_ALG0_CROSSOVERFILTER2WAYALGDP3LOWINVERT_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_LOW_FILT1_PARAMB0_ADDR           244
#define MOD_2000_ALG0_LOW_FILT1_PARAMB0_FIXPT          0x000231DC
#define MOD_2000_ALG0_LOW_FILT1_PARAMB0_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.0171465958472464)
#define MOD_2000_ALG0_LOW_FILT1_PARAMB0_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_LOW_FILT1_PARAMB1_ADDR           245
#define MOD_2000_ALG0_LOW_FILT1_PARAMB1_FIXPT          0x000463B8
#define MOD_2000_ALG0_LOW_FILT1_PARAMB1_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.0342931916944929)
#define MOD_2000_ALG0_LOW_FILT1_PARAMB1_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_LOW_FILT1_PARAMA1_ADDR           246
#define MOD_2000_ALG0_LOW_FILT1_PARAMA1_FIXPT          0x00CC682F
#define MOD_2000_ALG0_LOW_FILT1_PARAMA1_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.59692944487145)
#define MOD_2000_ALG0_LOW_FILT1_PARAMA1_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_LOW_FILT1_PARAMB2_ADDR           247
#define MOD_2000_ALG0_LOW_FILT1_PARAMB2_FIXPT          0x000231DC
#define MOD_2000_ALG0_LOW_FILT1_PARAMB2_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.0171465958472464)
#define MOD_2000_ALG0_LOW_FILT1_PARAMB2_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_LOW_FILT1_PARAMA2_ADDR           248
#define MOD_2000_ALG0_LOW_FILT1_PARAMA2_FIXPT          0xFFAAD061
#define MOD_2000_ALG0_LOW_FILT1_PARAMA2_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.665515828260434)
#define MOD_2000_ALG0_LOW_FILT1_PARAMA2_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_LOW_FILT2_PARAMB0_ADDR           249
#define MOD_2000_ALG0_LOW_FILT2_PARAMB0_FIXPT          0x000231DC
#define MOD_2000_ALG0_LOW_FILT2_PARAMB0_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.0171465958472464)
#define MOD_2000_ALG0_LOW_FILT2_PARAMB0_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_LOW_FILT2_PARAMB1_ADDR           250
#define MOD_2000_ALG0_LOW_FILT2_PARAMB1_FIXPT          0x000463B8
#define MOD_2000_ALG0_LOW_FILT2_PARAMB1_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.0342931916944929)
#define MOD_2000_ALG0_LOW_FILT2_PARAMB1_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_LOW_FILT2_PARAMA1_ADDR           251
#define MOD_2000_ALG0_LOW_FILT2_PARAMA1_FIXPT          0x00CC682F
#define MOD_2000_ALG0_LOW_FILT2_PARAMA1_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.59692944487145)
#define MOD_2000_ALG0_LOW_FILT2_PARAMA1_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_LOW_FILT2_PARAMB2_ADDR           252
#define MOD_2000_ALG0_LOW_FILT2_PARAMB2_FIXPT          0x000231DC
#define MOD_2000_ALG0_LOW_FILT2_PARAMB2_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.0171465958472464)
#define MOD_2000_ALG0_LOW_FILT2_PARAMB2_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_LOW_FILT2_PARAMA2_ADDR           253
#define MOD_2000_ALG0_LOW_FILT2_PARAMA2_FIXPT          0xFFAAD061
#define MOD_2000_ALG0_LOW_FILT2_PARAMA2_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.665515828260434)
#define MOD_2000_ALG0_LOW_FILT2_PARAMA2_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_HIGH_FILT1_PARAMB0_ADDR          254
#define MOD_2000_ALG0_HIGH_FILT1_PARAMB0_FIXPT         0x006865F3
#define MOD_2000_ALG0_HIGH_FILT1_PARAMB0_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.81561131828297)
#define MOD_2000_ALG0_HIGH_FILT1_PARAMB0_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_HIGH_FILT1_PARAMB1_ADDR          255
#define MOD_2000_ALG0_HIGH_FILT1_PARAMB1_FIXPT         0xFF2F3419
#define MOD_2000_ALG0_HIGH_FILT1_PARAMB1_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.63122263656594)
#define MOD_2000_ALG0_HIGH_FILT1_PARAMB1_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_HIGH_FILT1_PARAMA1_ADDR          256
#define MOD_2000_ALG0_HIGH_FILT1_PARAMA1_FIXPT         0x00CC682F
#define MOD_2000_ALG0_HIGH_FILT1_PARAMA1_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.59692944487145)
#define MOD_2000_ALG0_HIGH_FILT1_PARAMA1_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_HIGH_FILT1_PARAMB2_ADDR          257
#define MOD_2000_ALG0_HIGH_FILT1_PARAMB2_FIXPT         0x006865F3
#define MOD_2000_ALG0_HIGH_FILT1_PARAMB2_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.81561131828297)
#define MOD_2000_ALG0_HIGH_FILT1_PARAMB2_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_HIGH_FILT1_PARAMA2_ADDR          258
#define MOD_2000_ALG0_HIGH_FILT1_PARAMA2_FIXPT         0xFFAAD061
#define MOD_2000_ALG0_HIGH_FILT1_PARAMA2_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.665515828260434)
#define MOD_2000_ALG0_HIGH_FILT1_PARAMA2_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_HIGH_FILT2_PARAMB0_ADDR          259
#define MOD_2000_ALG0_HIGH_FILT2_PARAMB0_FIXPT         0x006865F3
#define MOD_2000_ALG0_HIGH_FILT2_PARAMB0_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.81561131828297)
#define MOD_2000_ALG0_HIGH_FILT2_PARAMB0_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_HIGH_FILT2_PARAMB1_ADDR          260
#define MOD_2000_ALG0_HIGH_FILT2_PARAMB1_FIXPT         0xFF2F3419
#define MOD_2000_ALG0_HIGH_FILT2_PARAMB1_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.63122263656594)
#define MOD_2000_ALG0_HIGH_FILT2_PARAMB1_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_HIGH_FILT2_PARAMA1_ADDR          261
#define MOD_2000_ALG0_HIGH_FILT2_PARAMA1_FIXPT         0x00CC682F
#define MOD_2000_ALG0_HIGH_FILT2_PARAMA1_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.59692944487145)
#define MOD_2000_ALG0_HIGH_FILT2_PARAMA1_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_HIGH_FILT2_PARAMB2_ADDR          262
#define MOD_2000_ALG0_HIGH_FILT2_PARAMB2_FIXPT         0x006865F3
#define MOD_2000_ALG0_HIGH_FILT2_PARAMB2_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.81561131828297)
#define MOD_2000_ALG0_HIGH_FILT2_PARAMB2_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_2000_ALG0_HIGH_FILT2_PARAMA2_ADDR          263
#define MOD_2000_ALG0_HIGH_FILT2_PARAMA2_FIXPT         0xFFAAD061
#define MOD_2000_ALG0_HIGH_FILT2_PARAMA2_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.665515828260434)
#define MOD_2000_ALG0_HIGH_FILT2_PARAMA2_TYPE          SIGMASTUDIOTYPE_FIXPOINT

/* Module 1500 - Crossover*/
#define MOD_1500_COUNT                                 21
#define MOD_1500_DEVICE                                "IC2"
#define MOD_1500_ALG0_CROSSOVERFILTER2WAYALGDP4LOWINVERT_ADDR 264
#define MOD_1500_ALG0_CROSSOVERFILTER2WAYALGDP4LOWINVERT_FIXPT 0x00800000
#define MOD_1500_ALG0_CROSSOVERFILTER2WAYALGDP4LOWINVERT_VALUE SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_1500_ALG0_CROSSOVERFILTER2WAYALGDP4LOWINVERT_TYPE SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_LOW_FILT1_PARAMB0_ADDR           265
#define MOD_1500_ALG0_LOW_FILT1_PARAMB0_FIXPT          0x0001126C
#define MOD_1500_ALG0_LOW_FILT1_PARAMB0_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.00837477942195286)
#define MOD_1500_ALG0_LOW_FILT1_PARAMB0_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_LOW_FILT1_PARAMB1_ADDR           266
#define MOD_1500_ALG0_LOW_FILT1_PARAMB1_FIXPT          0x000224D9
#define MOD_1500_ALG0_LOW_FILT1_PARAMB1_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.0167495588439057)
#define MOD_1500_ALG0_LOW_FILT1_PARAMB1_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_LOW_FILT1_PARAMA1_ADDR           267
#define MOD_1500_ALG0_LOW_FILT1_PARAMA1_FIXPT          0x00EA793D
#define MOD_1500_ALG0_LOW_FILT1_PARAMA1_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.83182500026466)
#define MOD_1500_ALG0_LOW_FILT1_PARAMA1_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_LOW_FILT1_PARAMB2_ADDR           268
#define MOD_1500_ALG0_LOW_FILT1_PARAMB2_FIXPT          0x0001126C
#define MOD_1500_ALG0_LOW_FILT1_PARAMB2_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.00837477942195286)
#define MOD_1500_ALG0_LOW_FILT1_PARAMB2_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_LOW_FILT1_PARAMA2_ADDR           269
#define MOD_1500_ALG0_LOW_FILT1_PARAMA2_FIXPT          0xFF913D10
#define MOD_1500_ALG0_LOW_FILT1_PARAMA2_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.865324117952472)
#define MOD_1500_ALG0_LOW_FILT1_PARAMA2_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_LOW_FILT2_PARAMB0_ADDR           270
#define MOD_1500_ALG0_LOW_FILT2_PARAMB0_FIXPT          0x0000FA90
#define MOD_1500_ALG0_LOW_FILT2_PARAMB0_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.00764659264937989)
#define MOD_1500_ALG0_LOW_FILT2_PARAMB0_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_LOW_FILT2_PARAMB1_ADDR           271
#define MOD_1500_ALG0_LOW_FILT2_PARAMB1_FIXPT          0x0001F520
#define MOD_1500_ALG0_LOW_FILT2_PARAMB1_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.0152931852987598)
#define MOD_1500_ALG0_LOW_FILT2_PARAMB1_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_LOW_FILT2_PARAMA1_ADDR           272
#define MOD_1500_ALG0_LOW_FILT2_PARAMA1_FIXPT          0x00D6160C
#define MOD_1500_ALG0_LOW_FILT2_PARAMA1_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.6725478817098)
#define MOD_1500_ALG0_LOW_FILT2_PARAMA1_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_LOW_FILT2_PARAMB2_ADDR           273
#define MOD_1500_ALG0_LOW_FILT2_PARAMB2_FIXPT          0x0000FA90
#define MOD_1500_ALG0_LOW_FILT2_PARAMB2_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.00764659264937989)
#define MOD_1500_ALG0_LOW_FILT2_PARAMB2_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_LOW_FILT2_PARAMA2_ADDR           274
#define MOD_1500_ALG0_LOW_FILT2_PARAMA2_FIXPT          0xFFA5FFB3
#define MOD_1500_ALG0_LOW_FILT2_PARAMA2_VALUE          SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.703134252307319)
#define MOD_1500_ALG0_LOW_FILT2_PARAMA2_TYPE           SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_HIGH_FILT1_PARAMB0_ADDR          275
#define MOD_1500_ALG0_HIGH_FILT1_PARAMB0_FIXPT         0x0073E127
#define MOD_1500_ALG0_HIGH_FILT1_PARAMB0_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.905308606467685)
#define MOD_1500_ALG0_HIGH_FILT1_PARAMB0_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_HIGH_FILT1_PARAMB1_ADDR          276
#define MOD_1500_ALG0_HIGH_FILT1_PARAMB1_FIXPT         0xFF183DB2
#define MOD_1500_ALG0_HIGH_FILT1_PARAMB1_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.81061721293537)
#define MOD_1500_ALG0_HIGH_FILT1_PARAMB1_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_HIGH_FILT1_PARAMA1_ADDR          277
#define MOD_1500_ALG0_HIGH_FILT1_PARAMA1_FIXPT         0x00E4832C
#define MOD_1500_ALG0_HIGH_FILT1_PARAMA1_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.78525305725374)
#define MOD_1500_ALG0_HIGH_FILT1_PARAMA1_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_HIGH_FILT1_PARAMB2_ADDR          278
#define MOD_1500_ALG0_HIGH_FILT1_PARAMB2_FIXPT         0x0073E127
#define MOD_1500_ALG0_HIGH_FILT1_PARAMB2_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.905308606467685)
#define MOD_1500_ALG0_HIGH_FILT1_PARAMB2_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_HIGH_FILT1_PARAMA2_ADDR          279
#define MOD_1500_ALG0_HIGH_FILT1_PARAMA2_FIXPT         0xFF94FE91
#define MOD_1500_ALG0_HIGH_FILT1_PARAMA2_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.835981368617006)
#define MOD_1500_ALG0_HIGH_FILT1_PARAMA2_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_HIGH_FILT2_PARAMB0_ADDR          280
#define MOD_1500_ALG0_HIGH_FILT2_PARAMB0_FIXPT         0x0067D62F
#define MOD_1500_ALG0_HIGH_FILT2_PARAMB0_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.811223921590814)
#define MOD_1500_ALG0_HIGH_FILT2_PARAMB0_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_HIGH_FILT2_PARAMB1_ADDR          281
#define MOD_1500_ALG0_HIGH_FILT2_PARAMB1_FIXPT         0xFF3053A2
#define MOD_1500_ALG0_HIGH_FILT2_PARAMB1_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-1.62244784318163)
#define MOD_1500_ALG0_HIGH_FILT2_PARAMB1_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_HIGH_FILT2_PARAMA1_ADDR          282
#define MOD_1500_ALG0_HIGH_FILT2_PARAMA1_FIXPT         0x00CCC39D
#define MOD_1500_ALG0_HIGH_FILT2_PARAMA1_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1.59971967105016)
#define MOD_1500_ALG0_HIGH_FILT2_PARAMA1_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_HIGH_FILT2_PARAMB2_ADDR          283
#define MOD_1500_ALG0_HIGH_FILT2_PARAMB2_FIXPT         0x0067D62F
#define MOD_1500_ALG0_HIGH_FILT2_PARAMB2_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0.811223921590814)
#define MOD_1500_ALG0_HIGH_FILT2_PARAMB2_TYPE          SIGMASTUDIOTYPE_FIXPOINT
#define MOD_1500_ALG0_HIGH_FILT2_PARAMA2_ADDR          284
#define MOD_1500_ALG0_HIGH_FILT2_PARAMA2_FIXPT         0xFFAD6AE0
#define MOD_1500_ALG0_HIGH_FILT2_PARAMA2_VALUE         SIGMASTUDIOTYPE_FIXPOINT_CONVERT(-0.645176015313095)
#define MOD_1500_ALG0_HIGH_FILT2_PARAMA2_TYPE          SIGMASTUDIOTYPE_FIXPOINT

/* Module Mute1 - Mute*/
#define MOD_MUTE1_COUNT                                1
#define MOD_MUTE1_DEVICE                               "IC2"
#define MOD_MUTE1_MUTENOSLEWALG1MUTE_ADDR              285
#define MOD_MUTE1_MUTENOSLEWALG1MUTE_FIXPT             0x00800000
#define MOD_MUTE1_MUTENOSLEWALG1MUTE_VALUE             SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_MUTE1_MUTENOSLEWALG1MUTE_TYPE              SIGMASTUDIOTYPE_FIXPOINT

/* Module Mute2 - Mute*/
#define MOD_MUTE2_COUNT                                1
#define MOD_MUTE2_DEVICE                               "IC2"
#define MOD_MUTE2_MUTENOSLEWALG2MUTE_ADDR              286
#define MOD_MUTE2_MUTENOSLEWALG2MUTE_FIXPT             0x00800000
#define MOD_MUTE2_MUTENOSLEWALG2MUTE_VALUE             SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_MUTE2_MUTENOSLEWALG2MUTE_TYPE              SIGMASTUDIOTYPE_FIXPOINT

/* Module Mute3 - Mute*/
#define MOD_MUTE3_COUNT                                1
#define MOD_MUTE3_DEVICE                               "IC2"
#define MOD_MUTE3_MUTENOSLEWALG3MUTE_ADDR              287
#define MOD_MUTE3_MUTENOSLEWALG3MUTE_FIXPT             0x00000000
#define MOD_MUTE3_MUTENOSLEWALG3MUTE_VALUE             SIGMASTUDIOTYPE_FIXPOINT_CONVERT(0)
#define MOD_MUTE3_MUTENOSLEWALG3MUTE_TYPE              SIGMASTUDIOTYPE_FIXPOINT

/* Module Mute4 - Mute*/
#define MOD_MUTE4_COUNT                                1
#define MOD_MUTE4_DEVICE                               "IC2"
#define MOD_MUTE4_MUTENOSLEWALG4MUTE_ADDR              288
#define MOD_MUTE4_MUTENOSLEWALG4MUTE_FIXPT             0x00800000
#define MOD_MUTE4_MUTENOSLEWALG4MUTE_VALUE             SIGMASTUDIOTYPE_FIXPOINT_CONVERT(1)
#define MOD_MUTE4_MUTENOSLEWALG4MUTE_TYPE              SIGMASTUDIOTYPE_FIXPOINT

#endif
