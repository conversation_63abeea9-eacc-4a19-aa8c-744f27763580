# EEPROM-Programmierung für ESP32 TCPI-Adapter

## Übersicht

Diese Erweiterung ermöglicht es, Sigma Studio HEX-Dateien direkt über den ESP32 TCPI-Adapter ins ADAU1701 EEPROM zu programmieren.

## Installation auf ESP32

### 1. Dateien auf ESP32 kopieren

Kopieren Sie folgende Dateien auf den ESP32:

```
eeprom_upy.py          # EEPROM-Manager für MicroPython
test_tcpi_upy.py       # Erweiterte Hauptdatei (überschreibt bestehende)
config.py              # Erweiterte Konfiguration (überschreibt bestehende)
```

### 2. ESP32 neu starten

Nach dem Kopieren der Dateien starten Sie den ESP32 neu. Sie sollten folgende Meldungen sehen:

```
EEPROM functionality loaded
EEPROM Manager initialized at address 0x50
```

## Nutzung

### 1. HEX-Datei aus Sigma Studio exportieren

1. Öffnen Sie Ihr Projekt in Sigma Studio
2. Gehen Sie zu **Action** → **Export System Files**
3. W<PERSON><PERSON>en Sie **Export for Standalone**
4. Speichern Sie die generierte **E2Prom.hex** Datei

### 2. EEPROM programmieren

#### Option A: Mit dem EEPROM-Programmer Tool

```bash
# Grundlegende Programmierung
python eeprom_programmer.py --hex-file E2Prom.hex --host *************

# Mit DSP-Reset nach Programmierung
python eeprom_programmer.py --hex-file E2Prom.hex --host ************* --reset-dsp

# Test-Modus
python eeprom_programmer.py --test --host *************
```

#### Option B: Direkte TCP-Befehle

Sie können auch direkte TCP-Befehle an den ESP32 senden:

```python
import socket

# Verbindung zum ESP32
sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
sock.connect(('*************', 8086))

# HEX-Datei lesen
with open('E2Prom.hex', 'r') as f:
    hex_content = f.read().strip()

# HEX-Programmierung
command = f"PROGRAM_HEX:{hex_content}".encode('utf-8')
sock.send(command)

# Antwort lesen
response = sock.recv(1024)
print(f"Response: {response}")

sock.close()
```

### 3. Verfügbare Befehle

Der ESP32 unterstützt folgende neue Befehle:

#### PROGRAM_HEX
Programmiert eine komplette HEX-Datei ins EEPROM:
```
PROGRAM_HEX:<hex_file_content>
```

Antworten:
- `PROGRAM_HEX_OK` - Erfolgreich programmiert
- `PROGRAM_HEX_ERROR` - Fehler bei der Programmierung

#### EEPROM_WRITE
Schreibt Binärdaten direkt ins EEPROM:
```
EEPROM_WRITE:<address_hex>:<data_hex>
```

Beispiel:
```
EEPROM_WRITE:0000:0102030405060708
```

Antworten:
- `EEPROM_WRITE_OK` - Erfolgreich geschrieben
- `EEPROM_WRITE_ERROR` - Fehler beim Schreiben

#### EEPROM_DUMP
Liest EEPROM-Inhalt aus (für Debugging):
```
EEPROM_DUMP:<address_hex>:<length_dec>
```

Beispiel:
```
EEPROM_DUMP:0000:256
```

## Workflow

```mermaid
graph TD
    A[Sigma Studio Projekt] --> B[Export als HEX-Datei]
    B --> C[EEPROM Programmer Tool]
    C -->|TCP/IP| D[ESP32 TCPI-Adapter]
    D --> E[HEX-Parser]
    E --> F[EEPROM-Manager]
    F --> G[I2C Page-Write]
    G --> H[EEPROM Chip]
    H --> I[Verifikation]
    I --> J[DSP Reset]
    J --> K[Self-Boot vom EEPROM]
```

## Technische Details

### EEPROM-Spezifikationen
- **Adresse**: 0x50 (I2C)
- **Page-Größe**: 32 Bytes
- **Schreib-Delay**: 5ms zwischen Pages
- **Maximale Größe**: 32KB

### HEX-Format
Das System unterstützt Intel HEX-Format mit folgenden Record-Types:
- **00**: Data Record
- **01**: End of File
- **02**: Extended Segment Address
- **04**: Extended Linear Address

### Sicherheitsfeatures
- **Automatische Verifikation** nach dem Schreiben
- **Page-basiertes Schreiben** verhindert EEPROM-Beschädigung
- **Error-Handling** mit detaillierter Fehlerausgabe
- **Memory-Management** für MicroPython-Kompatibilität

## Troubleshooting

### Problem: "EEPROM functionality not available"
**Lösung**: Stellen Sie sicher, dass `eeprom_upy.py` korrekt auf den ESP32 kopiert wurde.

### Problem: "PROGRAM_HEX_ERROR"
**Mögliche Ursachen**:
- Ungültige HEX-Datei
- I2C-Verbindungsfehler
- EEPROM-Hardware-Problem

**Debugging**:
```python
# Debug-Modus aktivieren
tcpi_server.DEBUG_MODE = True
```

### Problem: DSP startet nicht nach Programmierung
**Lösung**: 
1. Überprüfen Sie die HEX-Datei
2. Führen Sie einen manuellen DSP-Reset durch
3. Überprüfen Sie die EEPROM-Verkabelung

### Problem: Verbindungsfehler
**Lösung**:
1. Überprüfen Sie die IP-Adresse des ESP32
2. Stellen Sie sicher, dass Port 8086 offen ist
3. Überprüfen Sie die WiFi-Verbindung

## Beispiel-Session

```bash
$ python eeprom_programmer.py --hex-file MyProject.hex --host ************* --reset-dsp

Verbinde zu *************:8086...
Verbindung hergestellt!
Lade HEX-Datei: MyProject.hex
Größe: 2517 Zeichen
Sende HEX-Daten an EEPROM...
✓ EEPROM erfolgreich programmiert!
Setze DSP zurück...
Verbindung getrennt.

✓ Alle Operationen erfolgreich abgeschlossen!
```

## Erweiterte Features (Optional)

### Automatische HEX-Überwachung
```python
# Überwacht Verzeichnis auf neue HEX-Dateien
python hex_watcher.py --watch-dir ./sigma_exports --host *************
```

### Backup/Restore
```python
# EEPROM-Backup erstellen
python eeprom_programmer.py --backup eeprom_backup.bin --host *************

# EEPROM-Restore
python eeprom_programmer.py --restore eeprom_backup.bin --host *************
```

## Support

Bei Problemen überprüfen Sie:
1. ESP32-Logs über serielle Verbindung
2. I2C-Verkabelung (SCL: Pin 18, SDA: Pin 5)
3. EEPROM-Stromversorgung
4. Sigma Studio HEX-Export-Einstellungen
