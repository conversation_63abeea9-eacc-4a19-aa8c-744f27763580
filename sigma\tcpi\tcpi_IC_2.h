/*
 * File:           I:\Coden\Audio\EspSigmatTcpiAdapter\sigma\tcpi\tcpi_IC_2.h
 *
 * Created:        Sunday, June 15, 2025 7:52:15 PM
 * Description:    tcpi:IC 2 program data.
 *
 * This software is distributed in the hope that it will be useful,
 * but is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 *
 * This software may only be used to program products purchased from
 * Analog Devices for incorporation by you into audio products that
 * are intended for resale to audio product end users. This software
 * may not be distributed whole or in any part to third parties.
 *
 * Copyright ©2025 Analog Devices, Inc. All rights reserved.
 */
#ifndef __TCPI_IC_2_H__
#define __TCPI_IC_2_H__

#include "SigmaStudioFW.h"
#include "tcpi_IC_2_REG.h"

#define DEVICE_ARCHITECTURE_IC_2                  "E2Prom"
#define DEVICE_ADDR_IC_2                          0x0



/*
 * Default Download
 */
#define DEFAULT_DOWNLOAD_SIZE_IC_2 0

void default_download_IC_2() {
}

#endif
