# ADAU1701 Self-Boot Test - Schritt für Schritt

## Problem-Diagnose

**Aktueller Status:**
- ✅ EEPROM wird beschrieben (4096 Bytes)
- ✅ JTAG-Modus funktioniert (Live)
- ❌ SELFBOOT-Modus funktioniert nicht
- 🔧 Hardware-Schalter: SELFBOOT+WP / JTAG+WE

## Schritt 1: EEPROM-Inhalt prüfen

```bash
# EEPROM auslesen
python eeprom_dump_tool.py --dump --host **************

# Daten analysieren  
python test_eeprom_addresses.py
```

## Schritt 2: Minimales Self-Boot Projekt erstellen

### In Sigma Studio 4.7:

1. **Neues Projekt erstellen:**
   - File → New Project
   - ADAU1701 auswählen
   - Minimale Konfiguration

2. **Einfache Schaltung:**
   ```
   Input → [Gain Block] → Output
   ```
   - Input: ADC0
   - Gain: 0 dB (Durchgang)
   - Output: DAC0

3. **Self-Boot aktivieren:**
   
   **Methode A - Hardware Tab:**
   - Hardware Tab öffnen
   - ADAU1701 auswählen
   - Properties → Boot Configuration
   - ☑️ "Enable Boot from EEPROM"
   
   **Methode B - IC Settings:**
   - IC 1 (ADAU1701) Rechtsklick
   - Settings/Properties
   - Boot Options
   - ☑️ "Self-Boot Enable"
   
   **Methode C - Project Settings:**
   - Project → Settings
   - Hardware Configuration
   - Boot Source: EEPROM

4. **Kompilieren:**
   - Action → Compile
   - Keine Fehler sollten auftreten

5. **Exportieren:**
   - Action → Export System Files
   - Alle Dateien speichern

## Schritt 3: Test-Projekt programmieren

```bash
# C-Array zu Intel HEX konvertieren
python c_array_to_hex_converter.py minimal_test.hex

# EEPROM programmieren
python eeprom_programmer.py --hex-file minimal_test.hex --host ************** --reset-dsp
```

## Schritt 4: Hardware-Test

### Test-Sequenz:

1. **Schalter auf JTAG + WE**
2. **Projekt über USBI-JTAG laden** (Referenz-Test)
3. **Audio-Signal testen** → sollte funktionieren ✅
4. **Schalter auf SELFBOOT + WP**
5. **DSP Power-Cycle** (Strom aus/an)
6. **Audio-Signal testen** → sollte funktionieren ❓

### Wenn Self-Boot nicht funktioniert:

**Hardware prüfen:**
- SELFBOOT Pin Spannung messen (sollte HIGH sein)
- EEPROM I2C-Verbindung (SCL/SDA)
- Power-On Reset Timing

**Software prüfen:**
- EEPROM-Inhalt mit USBI-JTAG vergleichen
- Sigma Studio Self-Boot Einstellungen
- Export-Format validieren

## Schritt 5: Erweiterte Diagnose

### EEPROM-Vergleich:

1. **Mit USBI-JTAG programmieren:**
   - Gleiches Projekt
   - EEPROM auslesen
   - Als Referenz speichern

2. **Mit ESP32-TCPI programmieren:**
   - Gleiches Projekt  
   - EEPROM auslesen
   - Mit Referenz vergleichen

### Typische Self-Boot Probleme:

1. **Sigma Studio Export unvollständig:**
   - Nur Parameter, keine Hardware-Config
   - Self-Boot nicht aktiviert
   - Falsche Export-Optionen

2. **EEPROM-Format falsch:**
   - Falsche Start-Adresse
   - Fehlende Header-Daten
   - Checksum-Fehler

3. **Hardware-Konfiguration:**
   - SELFBOOT Pin nicht HIGH
   - I2C Pull-Up Widerstände
   - Reset-Timing

4. **DSP-Konfiguration:**
   - Clock-Einstellungen
   - Pin-Konfiguration
   - Power-Management

## Schritt 6: Lösungsansätze

### Wenn minimales Projekt nicht funktioniert:

1. **EEPROM komplett löschen:**
   ```bash
   python eeprom_programmer.py --erase --host **************
   ```

2. **Bekanntes funktionierendes EEPROM-Image verwenden:**
   - Von anderem ADAU1701-Board
   - Aus Sigma Studio Beispielen
   - Factory Default

3. **Hardware-Debugging:**
   - Oszilloskop an SELFBOOT Pin
   - I2C-Analyzer für EEPROM-Kommunikation
   - Logic Analyzer für Boot-Sequenz

### Wenn nur Live-Modus funktioniert:

**Mögliche Ursachen:**
- Self-Boot in Sigma Studio nicht aktiviert
- Export enthält keine Boot-Konfiguration
- Hardware-Schalter-Problem
- EEPROM-Timing-Problem

## Nächste Schritte

1. **Führen Sie Schritt 1-2 aus** (EEPROM prüfen, minimales Projekt)
2. **Zeigen Sie mir die Ergebnisse**
3. **Wir analysieren die Sigma Studio Einstellungen**
4. **Hardware-Test mit minimalem Projekt**

## Erfolgs-Kriterien

✅ **Minimales Projekt funktioniert in beiden Modi:**
- JTAG-Modus: Live-Programmierung ✅
- SELFBOOT-Modus: EEPROM-Boot ✅

✅ **Audio-Durchgang in beiden Modi identisch**

✅ **Komplexere Projekte funktionieren dann auch**
