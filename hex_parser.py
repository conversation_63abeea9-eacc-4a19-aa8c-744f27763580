"""
HEX File Parser für Sigma Studio EEPROM Dateien
Konvertiert Sigma Studio HEX-Dateien in binäre EEPROM-Daten
"""

import re
from typing import List, Tuple, Optional


class SigmaStudioHexParser:
    """Parser für Sigma Studio HEX-Dateien"""
    
    def __init__(self):
        self.data = bytearray()
        self.base_address = 0
        
    def parse_hex_file(self, hex_content: str) -> bytearray:
        """
        Parst eine Sigma Studio HEX-Datei und extrahiert die EEPROM-Daten
        
        Args:
            hex_content: Inhalt der HEX-Datei als String
            
        Returns:
            bytearray: Binäre EEPROM-Daten
        """
        self.data = bytearray()
        self.base_address = 0
        
        lines = hex_content.strip().split('\n')
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
                
            try:
                self._parse_hex_line(line)
            except Exception as e:
                print(f"<PERSON><PERSON> in Zeile {line_num}: {line}")
                print(f"Error: {e}")
                raise
                
        return self.data
    
    def _parse_hex_line(self, line: str):
        """Parst eine einzelne HEX-Zeile"""
        if not line.startswith(':'):
            raise ValueError(f"Ungültige HEX-Zeile: {line}")
            
        # Intel HEX Format: :LLAAAATT[DD...]CC
        # LL = Länge, AAAA = Adresse, TT = Typ, DD = Daten, CC = Checksum
        
        hex_data = line[1:]  # ':' entfernen
        
        if len(hex_data) < 8:
            raise ValueError(f"HEX-Zeile zu kurz: {line}")
            
        # Länge der Daten
        length = int(hex_data[0:2], 16)
        
        # Adresse
        address = int(hex_data[2:6], 16)
        
        # Record Type
        record_type = int(hex_data[6:8], 16)
        
        # Daten extrahieren
        data_start = 8
        data_end = data_start + (length * 2)
        
        if len(hex_data) < data_end + 2:
            raise ValueError(f"HEX-Zeile unvollständig: {line}")
            
        data_hex = hex_data[data_start:data_end]
        checksum = int(hex_data[data_end:data_end+2], 16)
        
        # Checksum verifizieren
        calculated_checksum = self._calculate_checksum(hex_data[:-2])
        if calculated_checksum != checksum:
            raise ValueError(f"Checksum-Fehler in Zeile: {line}")
        
        # Record Types verarbeiten
        if record_type == 0x00:  # Data Record
            self._process_data_record(address, data_hex)
        elif record_type == 0x01:  # End of File
            pass
        elif record_type == 0x02:  # Extended Segment Address
            self.base_address = int(data_hex, 16) << 4
        elif record_type == 0x04:  # Extended Linear Address
            self.base_address = int(data_hex, 16) << 16
        else:
            print(f"Unbekannter Record Type: {record_type:02X}")
    
    def _process_data_record(self, address: int, data_hex: str):
        """Verarbeitet einen Daten-Record"""
        full_address = self.base_address + address
        
        # Daten in Bytes konvertieren
        data_bytes = bytes.fromhex(data_hex)
        
        # Array erweitern falls nötig
        required_size = full_address + len(data_bytes)
        if len(self.data) < required_size:
            self.data.extend(b'\xFF' * (required_size - len(self.data)))
        
        # Daten einfügen
        for i, byte_val in enumerate(data_bytes):
            self.data[full_address + i] = byte_val
    
    def _calculate_checksum(self, hex_data: str) -> int:
        """Berechnet die Intel HEX Checksum"""
        checksum = 0
        for i in range(0, len(hex_data), 2):
            checksum += int(hex_data[i:i+2], 16)
        return (0x100 - (checksum & 0xFF)) & 0xFF
    
    def extract_eeprom_data(self, start_address: int = 0, size: int = None) -> bytearray:
        """
        Extrahiert EEPROM-Daten ab einer bestimmten Adresse
        
        Args:
            start_address: Start-Adresse für EEPROM-Daten
            size: Größe der zu extrahierenden Daten (None = bis Ende)
            
        Returns:
            bytearray: EEPROM-Daten
        """
        if size is None:
            return self.data[start_address:]
        else:
            return self.data[start_address:start_address + size]


def convert_sigma_hex_to_eeprom(hex_file_path: str, output_path: str = None) -> bytearray:
    """
    Konvertiert eine Sigma Studio HEX-Datei zu EEPROM-Binärdaten
    
    Args:
        hex_file_path: Pfad zur HEX-Datei
        output_path: Optionaler Pfad für Binär-Output
        
    Returns:
        bytearray: EEPROM-Daten
    """
    parser = SigmaStudioHexParser()
    
    with open(hex_file_path, 'r') as f:
        hex_content = f.read()
    
    eeprom_data = parser.parse_hex_file(hex_content)
    
    if output_path:
        with open(output_path, 'wb') as f:
            f.write(eeprom_data)
        print(f"EEPROM-Daten gespeichert: {output_path}")
    
    return eeprom_data


if __name__ == "__main__":
    # Test-Beispiel
    test_hex = """
:020000040000FA
:10000000010203040506070809101112131415161F
:00000001FF
"""
    
    parser = SigmaStudioHexParser()
    data = parser.parse_hex_file(test_hex)
    print(f"Parsed data: {data.hex()}")
