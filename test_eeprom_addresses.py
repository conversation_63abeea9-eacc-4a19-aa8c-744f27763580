#!/usr/bin/env python3
"""
Testet verschiedene EEPROM-Adressen für Sigma Studio
"""

import sys
from pathlib import Path


def test_different_addresses(hex_file, host="**************"):
    """Testet verschiedene Start-Adressen für EEPROM-Programmierung"""
    
    if not Path(hex_file).exists():
        print(f"HEX-Datei nicht gefunden: {hex_file}")
        return
    
    # Verschiedene Start-Adressen testen
    test_addresses = [
        0x0000,  # Standard
        0x0400,  # 1KB Offset
        0x0800,  # 2KB Offset  
        0x1000,  # 4KB Offset
    ]
    
    print(f"Teste verschiedene EEPROM-Adressen mit {hex_file}")
    print("=" * 60)
    
    for addr in test_addresses:
        print(f"\nTest: Start-Adresse 0x{addr:04X}")
        print(f"Befehl: python eeprom_programmer.py --hex-file {hex_file} --host {host} --address 0x{addr:04X}")
        
        # Hier könnten wir automatisch testen, aber erstmal manuell
        response = input(f"Möchten Sie Adresse 0x{addr:04X} testen? (y/n): ")
        if response.lower() == 'y':
            import subprocess
            try:
                # Erweiterten Programmer mit Adress-Parameter aufrufen
                # (müsste erst implementiert werden)
                print("TODO: Automatischer Test")
            except Exception as e:
                print(f"Fehler: {e}")


def analyze_sigma_studio_structure():
    """Analysiert die Struktur der Sigma Studio Daten"""
    
    print("\n=== Sigma Studio Daten-Struktur Analyse ===")
    
    # C-Array Datei analysieren
    if Path("test_sigma.hex").exists():
        with open("test_sigma.hex", 'r') as f:
            content = f.read()
        
        import re
        hex_values = re.findall(r'0x([0-9A-Fa-f]{2})', content)
        
        if len(hex_values) >= 16:
            print("Erste 64 Bytes der Sigma Studio Daten:")
            
            for i in range(0, min(64, len(hex_values)), 16):
                line_values = hex_values[i:i+16]
                hex_line = ' '.join(line_values)
                ascii_line = ''.join(chr(int(val, 16)) if 32 <= int(val, 16) <= 126 else '.' for val in line_values)
                print(f"{i:04X}: {hex_line:<48} {ascii_line}")
            
            # Suche nach typischen ADAU1701 Patterns
            print(f"\nDaten-Analyse:")
            print(f"Gesamt Bytes: {len(hex_values)}")
            
            # Erste 4 Bytes (oft Header)
            if len(hex_values) >= 4:
                header = ' '.join(hex_values[:4])
                print(f"Header (erste 4 Bytes): {header}")
                
                # Typische ADAU1701 Header-Patterns
                if hex_values[0] == '00' and hex_values[1] == '00':
                    print("  → Möglicher ADAU1701 Header erkannt")
            
            # Suche nach 0x80 0x00 0x00 Pattern (typisch für ADAU1701)
            pattern_count = 0
            for i in range(len(hex_values) - 3):
                if (hex_values[i] == '00' and hex_values[i+1] == '80' and 
                    hex_values[i+2] == '00' and hex_values[i+3] == '00'):
                    pattern_count += 1
            
            print(f"0x00 0x80 0x00 0x00 Pattern gefunden: {pattern_count} mal")
            if pattern_count > 10:
                print("  → Viele Default-Werte, möglicherweise uninitialisierte Register")


def check_adau1701_selfboot_requirements():
    """Prüft ADAU1701 Self-Boot Anforderungen"""
    
    print("\n=== ADAU1701 Self-Boot Anforderungen ===")
    
    requirements = [
        "1. EEPROM muss an I2C-Adresse 0x50 (7-bit) angeschlossen sein",
        "2. SELFBOOT Pin muss auf HIGH sein beim Power-On",
        "3. EEPROM-Daten müssen ab Adresse 0x0000 stehen",
        "4. Erste Bytes müssen gültigen ADAU1701 Header enthalten",
        "5. Checksum muss korrekt sein",
        "6. Alle Register-Adressen müssen gültig sein"
    ]
    
    for req in requirements:
        print(f"  {req}")
    
    print("\nHäufige Probleme:")
    problems = [
        "• Sigma Studio exportiert manchmal nur Parameter, nicht komplette Konfiguration",
        "• Self-Boot muss explizit in Sigma Studio aktiviert werden",
        "• EEPROM-Format unterscheidet sich zwischen Sigma Studio Versionen",
        "• Hardware-Konfiguration (Pins, Clock) muss im Export enthalten sein"
    ]
    
    for problem in problems:
        print(f"  {problem}")


def suggest_next_steps():
    """Schlägt nächste Debugging-Schritte vor"""
    
    print("\n=== Nächste Debugging-Schritte ===")
    
    steps = [
        "1. EEPROM-Inhalt auslesen und mit Original vergleichen:",
        "   python eeprom_dump_tool.py --dump --host **************",
        "",
        "2. Einfaches Test-Projekt erstellen:",
        "   - Nur Input → Output Verbindung",
        "   - Keine DSP-Effekte",
        "   - Self-Boot aktivieren",
        "",
        "3. Sigma Studio Projekt prüfen:",
        "   - Hardware Configuration → Self-Boot Enable",
        "   - IC 1 → ADAU1701 Settings → Boot from EEPROM",
        "   - Compile → Link Compile Download",
        "",
        "4. Hardware-Test:",
        "   - SELFBOOT Pin messen (sollte HIGH sein)",
        "   - I2C-Kommunikation mit EEPROM prüfen",
        "   - DSP Reset-Verhalten beobachten",
        "",
        "5. Alternative: Original USBI-JTAG zum Vergleich:",
        "   - Gleiches Projekt mit USBI-JTAG programmieren",
        "   - EEPROM auslesen und vergleichen"
    ]
    
    for step in steps:
        print(step)


def main():
    """Hauptfunktion"""
    
    print("EEPROM Address & Structure Debug Tool")
    print("=" * 60)
    
    # Struktur analysieren
    analyze_sigma_studio_structure()
    
    # Self-Boot Anforderungen
    check_adau1701_selfboot_requirements()
    
    # Nächste Schritte
    suggest_next_steps()
    
    # Adress-Tests anbieten
    if len(sys.argv) > 1:
        hex_file = sys.argv[1]
        test_different_addresses(hex_file)


if __name__ == "__main__":
    main()
