import time

import adapters
import config
import machine
import peripherals
from TCPIP1701 import class_finder
from tcpi_server import I2C as TcpI2C
from wifi import wait_for_wifi

# EEPROM-Funktionalität importieren
try:
    from eeprom_upy import SimpleEEPROMManager
    EEPROM_AVAILABLE = True
    print("EEPROM functionality loaded")
except ImportError:
    EEPROM_AVAILABLE = False
    print("EEPROM functionality not available")


# pin_reset ======================
pin_reset = machine.Pin(config.RESET_PIN_ID, machine.Pin.OPEN_DRAIN)
pin_reset.value(1)



def reset_adau():
    for v in (1, 0, 1):
        pin_reset.value(v)
        time.sleep(1 / 1e3)



# hardware bus ======================

print("Starting WiFi connection...")
wifi_success = wait_for_wifi()

if not wifi_success:
    print("WiFi connection failed - stopping here")
    print("Please check:")
    print("1. SSID and PASSWORD in config.py")
    print("2. WiFi signal strength")
    print("3. WiFi network is 2.4GHz (not 5GHz)")
    import sys
    sys.exit()

print("WiFi connected - continuing with setup...")

with_hardware_device = True

if with_hardware_device:
    _i2c = peripherals.I2C.get_uPy_i2c(scl_pin_id = config.I2C_SCL_PIN_ID,
                                       sda_pin_id = config.I2C_SDA_PIN_ID)
else:
    _i2c = None  # using None for testing without actual hardware device.

bus = adapters.I2C(_i2c)
reset_adau()

# tcpi_server =======================

# I2C-Adressen (7-Bit-Format, wie von Hardware-Scan bestätigt)
I2C_ADDRESS = 0x34        # DSP ADAU1701 (bestätigt durch I2C-Scan)
I2C_ADDRESS_EEPROM = 0x50  # EEPROM (bestätigt durch I2C-Scan)
I2C_ADDRESSes = {1                 : (I2C_ADDRESS, 2),  # (i2c_addresses, n_sub_address_bytes)
                 2                 : (I2C_ADDRESS_EEPROM, 2),
                 I2C_ADDRESS       : (I2C_ADDRESS, 2),
                 I2C_ADDRESS_EEPROM: (I2C_ADDRESS_EEPROM, 2)}

tcpi_server = TcpI2C(bus, class_finder, i2c_addresses = I2C_ADDRESSes)
tcpi_server.DEBUG_MODE = False

# EEPROM Manager hinzufügen
if EEPROM_AVAILABLE:
    eeprom_manager = SimpleEEPROMManager(bus, I2C_ADDRESS_EEPROM)
    print(f"EEPROM Manager initialized at address 0x{I2C_ADDRESS_EEPROM:02X}")
else:
    eeprom_manager = None



def process_data(data):
    global eeprom_manager

    # Standard-Befehle
    if data == config.CMD_RESET_DSP:
        reset_adau()
        return

    if data == config.CMD_RESET:
        reset_adau()
        machine.reset()
        return

    # EEPROM-Befehle
    if eeprom_manager and data.startswith(b'EEPROM_'):
        handle_eeprom_commands(data)
        return

    # HEX-Programmierung
    if eeprom_manager and data.startswith(b'PROGRAM_HEX:'):
        handle_hex_program(data)
        return

    # Binär-Programmierung (E2PROM.bin)
    if eeprom_manager and data.startswith(b'EEPROM_WRITE_BINARY:'):
        handle_binary_program(data)
        return

    # RAM-Upload (direkt in DSP)
    if data.startswith(b'RAM_UPLOAD:'):
        handle_ram_upload(data)
        return

    # EEPROM auslesen
    if eeprom_manager and data.startswith(b'READ_EEPROM:'):
        handle_read_eeprom(data)
        return

    # I2C Bus scannen
    if data.startswith(b'I2C_SCAN'):
        handle_i2c_scan()
        return


def handle_eeprom_commands(data):
    """Behandelt EEPROM-spezifische Befehle"""
    try:
        command_str = data.decode('utf-8')

        if command_str.startswith('EEPROM_WRITE:'):
            # Format: EEPROM_WRITE:<address>:<hex_data>
            parts = command_str.split(':')
            if len(parts) == 3:
                address = int(parts[1], 16)
                hex_data = parts[2]
                binary_data = bytes.fromhex(hex_data)

                print(f"EEPROM Write: 0x{address:04X}, {len(binary_data)} bytes")
                success = eeprom_manager.write_data(address, binary_data)

                response = b"EEPROM_WRITE_OK" if success else b"EEPROM_WRITE_ERROR"
                tcpi_server.send(response)
            else:
                tcpi_server.send(b"EEPROM_WRITE_FORMAT_ERROR")

        elif command_str.startswith('EEPROM_DUMP:'):
            # Format: EEPROM_DUMP:<address>:<length>
            parts = command_str.split(':')
            if len(parts) == 3:
                address = int(parts[1], 16)
                length = int(parts[2])

                data = eeprom_manager.dump_eeprom(address, length)
                if data:
                    tcpi_server.send(b"EEPROM_DUMP_OK")
                else:
                    tcpi_server.send(b"EEPROM_DUMP_ERROR")
            else:
                tcpi_server.send(b"EEPROM_DUMP_FORMAT_ERROR")

    except Exception as e:
        print(f"EEPROM Command Error: {e}")
        tcpi_server.send(b"EEPROM_COMMAND_ERROR")


def handle_hex_program(data):
    """Behandelt HEX-Programmierung"""
    try:
        command_str = data.decode('utf-8')

        if ':' not in command_str:
            tcpi_server.send(b"PROGRAM_HEX_FORMAT_ERROR")
            return

        hex_content = command_str.split(':', 1)[1]

        print("Programming HEX to EEPROM...")
        success = eeprom_manager.program_hex_content(hex_content)

        if success:
            print("HEX programming successful!")
            tcpi_server.send(b"PROGRAM_HEX_OK")
            # DSP nach erfolgreicher Programmierung zurücksetzen
            time.sleep(0.1)
            reset_adau()
        else:
            print("HEX programming failed!")
            tcpi_server.send(b"PROGRAM_HEX_ERROR")

    except Exception as e:
        print(f"HEX Program Error: {e}")
        tcpi_server.send(b"PROGRAM_HEX_ERROR")


def handle_binary_program(data):
    """Behandelt E2PROM.bin Programmierung"""
    try:
        # Binärdaten extrahieren (nach "EEPROM_WRITE_BINARY:")
        binary_data = data[20:]  # "EEPROM_WRITE_BINARY:" = 20 Zeichen

        print(f"Programming E2PROM.bin to EEPROM: {len(binary_data)} bytes")
        print("Using slow, stable timing for ADAU1701 compatibility...")

        # Langsamer, stabiler EEPROM-Schreibvorgang
        # Erhöhte Delays für ADAU1701-Kompatibilität
        eeprom_manager.write_delay_ms = 200  # Extrem langsam für maximale Stabilität
        success = eeprom_manager.write_data(0x0000, binary_data)

        if success:
            print("E2PROM.bin programming successful!")
            tcpi_server.send(b"EEPROM_WRITE_BINARY_OK")
            # DSP nach erfolgreicher Programmierung zurücksetzen
            # Mehr Zeit für EEPROM-Stabilisierung
            time.sleep(1.0)
            reset_adau()
            time.sleep(0.5)  # Zeit für DSP-Boot
        else:
            print("E2PROM.bin programming failed!")
            tcpi_server.send(b"EEPROM_WRITE_BINARY_ERROR")

    except Exception as e:
        print(f"Binary Program Error: {e}")
        tcpi_server.send(b"EEPROM_WRITE_BINARY_ERROR")


def handle_ram_upload(data):
    """Behandelt RAM-Upload (direkt in DSP)"""
    try:
        # HEX-Daten extrahieren (nach "RAM_UPLOAD:")
        hex_data = data[11:].decode('utf-8')  # "RAM_UPLOAD:" = 11 Zeichen

        print(f"RAM Upload: {len(hex_data)} characters")

        # HEX-Daten parsen und direkt in DSP schreiben
        success = program_dsp_ram(hex_data)

        if success:
            print("RAM upload successful!")
            tcpi_server.send(b"RAM_UPLOAD_OK")
        else:
            print("RAM upload failed!")
            tcpi_server.send(b"RAM_UPLOAD_ERROR")

    except Exception as e:
        print(f"RAM Upload Error: {e}")
        tcpi_server.send(b"RAM_UPLOAD_ERROR")


def handle_read_eeprom(data):
    """Behandelt EEPROM-Auslesen"""
    try:
        # Anzahl Bytes extrahieren (nach "READ_EEPROM:")
        size_str = data[12:].decode('utf-8')  # "READ_EEPROM:" = 12 Zeichen
        size = int(size_str)

        print(f"Reading EEPROM: {size} bytes")

        # EEPROM auslesen
        eeprom_data = eeprom_manager.read_data(0x0000, size)

        if eeprom_data:
            print(f"EEPROM read successful: {len(eeprom_data)} bytes")
            response = b"EEPROM_DATA:" + eeprom_data
            tcpi_server.send(response)
        else:
            print("EEPROM read failed!")
            tcpi_server.send(b"EEPROM_READ_ERROR")

    except Exception as e:
        print(f"EEPROM Read Error: {e}")
        tcpi_server.send(b"EEPROM_READ_ERROR")


def convert_hex_to_binary(hex_data):
    """Konvertiert HEX-String zu Binärdaten"""
    try:
        # Entferne Whitespace und Kommas
        hex_clean = hex_data.replace(' ', '').replace(',', '').replace('\n', '').replace('\r', '')

        # Entferne 0x Präfixe
        hex_clean = hex_clean.replace('0x', '')

        # Konvertiere zu Bytes
        if len(hex_clean) % 2 != 0:
            hex_clean = '0' + hex_clean  # Padding für ungerade Länge

        binary_data = bytes.fromhex(hex_clean)
        return binary_data

    except Exception as e:
        print(f"HEX conversion error: {e}")
        return None


def program_dsp_ram(hex_data):
    """Programmiert DSP RAM direkt (ohne EEPROM)"""
    try:
        # HEX-Daten in Binärdaten konvertieren
        binary_data = convert_hex_to_binary(hex_data)

        if not binary_data:
            print("HEX-Konvertierung fehlgeschlagen")
            return False

        print(f"Programming DSP RAM: {len(binary_data)} bytes")

        # DSP-Adresse für Parameter RAM (typisch 0x0000 für ADAU1701)
        dsp_address = 0x0000

        # Daten blockweise in DSP RAM schreiben
        block_size = 32  # Kleinere Blöcke für RAM
        offset = 0

        while offset < len(binary_data):
            block = binary_data[offset:offset + block_size]

            # Direkt in DSP schreiben (nicht EEPROM)
            success = write_dsp_register(dsp_address + offset, block)

            if not success:
                print(f"DSP write failed at offset 0x{offset:04X}")
                return False

            offset += len(block)
            print(f"  DSP RAM: 0x{dsp_address + offset:04X} ({offset}/{len(binary_data)} bytes)")

        print("DSP RAM programming complete!")
        return True

    except Exception as e:
        print(f"DSP RAM programming error: {e}")
        return False


def write_dsp_register(address, data):
    """Schreibt direkt in DSP Register"""
    try:
        # ADAU1701 I2C-Adresse (aus globaler Konfiguration)
        dsp_i2c_address = I2C_ADDRESS  # 0x34

        # Register-Adresse (2 Bytes) + Daten
        register_data = bytearray()
        register_data.extend(address.to_bytes(2, 'big'))  # Adresse als 2 Bytes
        register_data.extend(data)

        # Direkt an DSP senden (über Bus-Adapter)
        bus.write_addressed_bytes(dsp_i2c_address, address, data)

        # Kurze Pause für DSP-Verarbeitung
        time.sleep(0.001)  # 1ms

        return True

    except Exception as e:
        print(f"DSP register write error: {e}")
        return False


def handle_i2c_scan():
    """Behandelt I2C Bus-Scan"""
    try:
        print("Scanning I2C bus...")

        devices = []
        for addr in range(0x08, 0x78):  # Standard I2C-Adressbereich
            try:
                bus.write_bytes(addr, b'')
                devices.append(f"0x{addr:02X}")
                print(f"Found device at 0x{addr:02X}")
            except:
                pass

        if devices:
            device_list = ",".join(devices)
            response = f"I2C_DEVICES:{device_list}".encode('utf-8')
            print(f"I2C scan complete: {device_list}")
        else:
            response = b"I2C_DEVICES:none"
            print("No I2C devices found")

        tcpi_server.send(response)

    except Exception as e:
        print(f"I2C Scan Error: {e}")
        tcpi_server.send(b"I2C_SCAN_ERROR")



tcpi_server.add_subscriber(process_data)

tcpi_server.run()

# tcpi_server.stop()
