#!/usr/bin/env python3
"""
Konvertiert Sigma Studio C-Array Format zu Intel HEX
Für den Fall, dass Sigma Studio C-Arrays statt Intel HEX exportiert
"""

import re
import sys
from pathlib import Path


def parse_c_array(content):
    """Extrahiert Hex-Werte aus C-Array Format"""
    # Regex für Hex-Werte: 0x12, 0xAB, etc.
    hex_pattern = r'0x([0-9A-Fa-f]{2})'
    
    matches = re.findall(hex_pattern, content)
    
    if not matches:
        raise ValueError("Keine Hex-Werte im C-Array Format gefunden")
    
    # Konvertiere zu Bytes
    data = bytes(int(match, 16) for match in matches)
    
    return data


def create_intel_hex(data, start_address=0x0000):
    """Erstellt Intel HEX aus Binärdaten"""
    lines = []
    
    # Extended Linear Address Record (für Adresse 0x0000)
    lines.append(":020000040000FA")
    
    # Data Records (16 Bytes pro Zeile)
    for i in range(0, len(data), 16):
        chunk = data[i:i+16]
        address = start_address + i
        
        # Record: :LLAAAATT[DD...]CC
        # LL = Länge, AAAA = Adresse, TT = Typ (00=Data), DD = Daten, CC = Checksum
        
        length = len(chunk)
        addr_high = (address >> 8) & 0xFF
        addr_low = address & 0xFF
        record_type = 0x00
        
        # Daten-String erstellen
        data_str = ''.join(f'{b:02X}' for b in chunk)
        
        # Checksum berechnen
        checksum = length + addr_high + addr_low + record_type
        checksum += sum(chunk)
        checksum = (0x100 - (checksum & 0xFF)) & 0xFF
        
        # Record zusammenbauen
        record = f":{length:02X}{address:04X}{record_type:02X}{data_str}{checksum:02X}"
        lines.append(record)
    
    # End of File Record
    lines.append(":00000001FF")
    
    return '\n'.join(lines)


def convert_c_array_to_intel_hex(input_file, output_file=None):
    """Konvertiert C-Array Datei zu Intel HEX"""
    
    input_path = Path(input_file)
    if not input_path.exists():
        raise FileNotFoundError(f"Eingabedatei nicht gefunden: {input_file}")
    
    # Output-Datei bestimmen
    if output_file is None:
        output_file = input_path.with_suffix('.hex')
    
    print(f"Konvertiere: {input_file} → {output_file}")
    
    # C-Array Datei lesen
    with open(input_file, 'r') as f:
        content = f.read()
    
    print(f"Eingabedatei: {len(content)} Zeichen")
    
    # C-Array parsen
    try:
        data = parse_c_array(content)
        print(f"Extrahierte Daten: {len(data)} Bytes")
        
        # Erste 16 Bytes anzeigen
        if len(data) > 0:
            preview = ' '.join(f'{b:02X}' for b in data[:16])
            print(f"Erste 16 Bytes: {preview}")
    
    except ValueError as e:
        print(f"Fehler beim Parsen: {e}")
        return False
    
    # Intel HEX erstellen
    try:
        intel_hex = create_intel_hex(data)
        print(f"Intel HEX erstellt: {len(intel_hex.split())} Zeilen")
        
        # Intel HEX speichern
        with open(output_file, 'w') as f:
            f.write(intel_hex)
        
        print(f"✓ Konvertierung erfolgreich: {output_file}")
        
        # Erste paar Zeilen anzeigen
        lines = intel_hex.split('\n')
        print("Erste Intel HEX Zeilen:")
        for line in lines[:5]:
            print(f"  {line}")
        if len(lines) > 5:
            print(f"  ... und {len(lines) - 5} weitere Zeilen")
        
        return True
        
    except Exception as e:
        print(f"Fehler beim Erstellen der Intel HEX: {e}")
        return False


def main():
    """Hauptfunktion für Command-Line Nutzung"""
    if len(sys.argv) < 2:
        print("Verwendung: python c_array_to_hex_converter.py <input_file> [output_file]")
        print("Beispiel: python c_array_to_hex_converter.py sigma_data.txt sigma_data.hex")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    try:
        success = convert_c_array_to_intel_hex(input_file, output_file)
        
        if success:
            print("\n🎉 Konvertierung abgeschlossen!")
            print("Nächster Schritt:")
            output_name = output_file or Path(input_file).with_suffix('.hex')
            print(f"python eeprom_programmer.py --hex-file {output_name} --host ************** --reset-dsp")
        else:
            print("\n❌ Konvertierung fehlgeschlagen!")
            sys.exit(1)
            
    except Exception as e:
        print(f"Fehler: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
