"""
Einfacher Test um zu prüfen ob config.py korrekt geladen wird
Führen Sie dies auf dem ESP32 aus
"""

print("=== Config Test ===")

try:
    import config
    print("✓ config.py erfolgreich importiert")
    
    print(f"SSID: '{config.SSID}'")
    print(f"SSID type: {type(config.SSID)}")
    print(f"SSID length: {len(config.SSID)}")
    
    print(f"PASSWORD: '{'*' * len(config.PASSWORD)}'")
    print(f"PASSWORD type: {type(config.PASSWORD)}")
    print(f"PASSWORD length: {len(config.PASSWORD)}")
    
    # Prüfe auf häufige Probleme
    if config.SSID == 'WuTang':
        print("✓ SSID ist korrekt gesetzt")
    else:
        print(f"⚠️  SSID ist nicht 'WuTang', sondern '{config.SSID}'")
    
    if len(config.PASSWORD) == 17:
        print("✓ PASSWORD hat korrekte Länge")
    else:
        print(f"⚠️  PASSWORD hat {len(config.PASSWORD)} Zeichen, erwartet 17")
    
    # Test WiFi-Import
    print("\n=== WiFi Module Test ===")
    try:
        import network
        print("✓ network module importiert")
        
        sta_if = network.WLAN(network.STA_IF)
        print("✓ WLAN interface erstellt")
        
        sta_if.active(True)
        print("✓ WLAN aktiviert")
        
        # Schneller Scan-Test
        print("Scanning networks...")
        networks = sta_if.scan()
        print(f"✓ {len(networks)} Netzwerke gefunden")
        
        # Prüfe ob unser Netzwerk da ist
        found = False
        for net in networks:
            ssid = net[0].decode('utf-8')
            if ssid == config.SSID:
                found = True
                signal = net[3]
                print(f"✓ Ziel-Netzwerk '{config.SSID}' gefunden (Signal: {signal} dBm)")
                break
        
        if not found:
            print(f"✗ Ziel-Netzwerk '{config.SSID}' NICHT gefunden!")
            print("Verfügbare Netzwerke:")
            for net in networks[:5]:  # Nur erste 5 anzeigen
                ssid = net[0].decode('utf-8')
                signal = net[3]
                print(f"  - '{ssid}' ({signal} dBm)")
        
    except Exception as e:
        print(f"✗ WiFi Test Fehler: {e}")
    
except Exception as e:
    print(f"✗ Config Import Fehler: {e}")

print("\n=== Test abgeschlossen ===")
