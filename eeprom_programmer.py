#!/usr/bin/env python3
"""
EEPROM Programmer Client für ESP32 TCPI Adapter
Ermöglicht das Programmieren von Sigma Studio HEX-Dateien über TCP/IP
"""

import socket
import sys
import time
import argparse
from pathlib import Path


class EEPROMProgrammer:
    """Client für EEPROM-Programmierung über TCPI-Adapter"""
    
    def __init__(self, host: str = "*************", port: int = 8086):
        """
        Initialisiert den EEPROM Programmer
        
        Args:
            host: IP-Adresse des ESP32 TCPI-Adapters
            port: TCP-Port des TCPI-Servers
        """
        self.host = host
        self.port = port
        self.socket = None
        
    def connect(self) -> bool:
        """
        Verbindet zum TCPI-Server
        
        Returns:
            bool: True wenn Verbindung erfolgreich
        """
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(30)  # 30 Sekunden Timeout
            
            print(f"Verbinde zu {self.host}:{self.port}...")
            self.socket.connect((self.host, self.port))
            print("Verbindung hergestellt!")
            
            return True
            
        except Exception as e:
            print(f"Verbindungsfehler: {e}")
            return False
    
    def disconnect(self):
        """Trennt die Verbindung"""
        if self.socket:
            self.socket.close()
            self.socket = None
            print("Verbindung getrennt.")
    
    def send_command(self, command: bytes) -> bytes:
        """
        Sendet einen Befehl und wartet auf Antwort
        
        Args:
            command: Zu sendender Befehl
            
        Returns:
            bytes: Antwort vom Server
        """
        if not self.socket:
            raise RuntimeError("Nicht verbunden")
            
        self.socket.send(command)
        
        # Auf Antwort warten
        response = self.socket.recv(1024)
        return response
    
    def program_hex_file(self, hex_file_path: str) -> bool:
        """
        Programmiert eine HEX-Datei ins EEPROM
        
        Args:
            hex_file_path: Pfad zur HEX-Datei
            
        Returns:
            bool: True wenn erfolgreich
        """
        try:
            # HEX-Datei lesen
            with open(hex_file_path, 'r') as f:
                hex_content = f.read().strip()
            
            print(f"Lade HEX-Datei: {hex_file_path}")
            print(f"Größe: {len(hex_content)} Zeichen")
            
            # Befehl zusammenstellen
            command = f"PROGRAM_HEX:{hex_content}".encode('utf-8')
            
            print("Sende HEX-Daten an EEPROM...")
            
            # Befehl senden
            response = self.send_command(command)
            
            # Antwort auswerten
            if response == b"PROGRAM_HEX_OK":
                print("✓ EEPROM erfolgreich programmiert!")
                return True
            else:
                print(f"✗ EEPROM-Programmierung fehlgeschlagen: {response}")
                return False
                
        except Exception as e:
            print(f"Fehler beim Programmieren: {e}")
            return False
    
    def write_eeprom_data(self, address: int, data: bytes) -> bool:
        """
        Schreibt Binärdaten direkt ins EEPROM
        
        Args:
            address: Start-Adresse
            data: Zu schreibende Daten
            
        Returns:
            bool: True wenn erfolgreich
        """
        try:
            hex_data = data.hex().upper()
            command = f"EEPROM_WRITE:{address:04X}:{hex_data}".encode('utf-8')
            
            print(f"Schreibe {len(data)} Bytes ab Adresse 0x{address:04X}")
            
            response = self.send_command(command)
            
            if response == b"EEPROM_WRITE_OK":
                print("✓ EEPROM-Schreibvorgang erfolgreich!")
                return True
            else:
                print(f"✗ EEPROM-Schreibvorgang fehlgeschlagen: {response}")
                return False
                
        except Exception as e:
            print(f"Fehler beim EEPROM-Schreiben: {e}")
            return False
    
    def reset_dsp(self) -> bool:
        """
        Sendet DSP-Reset-Befehl
        
        Returns:
            bool: True wenn erfolgreich
        """
        try:
            command = b"RESET_DSP"
            response = self.send_command(command)
            print("DSP Reset gesendet")
            return True
        except Exception as e:
            print(f"Fehler beim DSP-Reset: {e}")
            return False


def main():
    """Hauptfunktion für Command-Line Interface"""
    parser = argparse.ArgumentParser(description="EEPROM Programmer für ESP32 TCPI Adapter")
    parser.add_argument("--host", default="*************", help="IP-Adresse des ESP32")
    parser.add_argument("--port", type=int, default=8086, help="TCP-Port")
    parser.add_argument("--hex-file", help="Pfad zur Sigma Studio HEX-Datei")
    parser.add_argument("--reset-dsp", action="store_true", help="DSP nach Programmierung zurücksetzen")
    parser.add_argument("--test", action="store_true", help="Test-Modus mit Dummy-Daten")
    
    args = parser.parse_args()
    
    # Programmer initialisieren
    programmer = EEPROMProgrammer(args.host, args.port)
    
    try:
        # Verbinden
        if not programmer.connect():
            sys.exit(1)
        
        success = True
        
        # Test-Modus
        if args.test:
            print("Test-Modus: Schreibe Test-Daten...")
            test_data = b'\x01\x02\x03\x04\x05\x06\x07\x08'
            success = programmer.write_eeprom_data(0x0000, test_data)
        
        # HEX-Datei programmieren
        elif args.hex_file:
            if not Path(args.hex_file).exists():
                print(f"HEX-Datei nicht gefunden: {args.hex_file}")
                sys.exit(1)
            
            success = programmer.program_hex_file(args.hex_file)
        
        else:
            print("Keine Aktion angegeben. Verwende --hex-file oder --test")
            sys.exit(1)
        
        # DSP Reset
        if success and args.reset_dsp:
            print("Setze DSP zurück...")
            programmer.reset_dsp()
            time.sleep(1)
        
        if success:
            print("\n✓ Alle Operationen erfolgreich abgeschlossen!")
        else:
            print("\n✗ Fehler bei der Ausführung!")
            sys.exit(1)
    
    finally:
        programmer.disconnect()


if __name__ == "__main__":
    main()
