#!/usr/bin/env python3
"""
EEPROM Programmer Client für ESP32 TCPI Adapter
Ermöglicht das Programmieren von Sigma Studio HEX-Dateien über TCP/IP
"""

import socket
import sys
import time
import argparse
from pathlib import Path


class EEPROMProgrammer:
    """Client für EEPROM-Programmierung über TCPI-Adapter"""
    
    def __init__(self, host: str = "*************", port: int = 8086):
        """
        Initialisiert den EEPROM Programmer
        
        Args:
            host: IP-Adresse des ESP32 TCPI-Adapters
            port: TCP-Port des TCPI-Servers
        """
        self.host = host
        self.port = port
        self.socket = None
        
    def connect(self) -> bool:
        """
        Verbindet zum TCPI-Server
        
        Returns:
            bool: True wenn Verbindung erfolgreich
        """
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(30)  # 30 Sekunden Timeout
            
            print(f"Verbinde zu {self.host}:{self.port}...")
            self.socket.connect((self.host, self.port))
            print("Verbindung hergestellt!")
            
            return True
            
        except Exception as e:
            print(f"Verbindungsfehler: {e}")
            return False
    
    def disconnect(self):
        """Trennt die Verbindung"""
        if self.socket:
            self.socket.close()
            self.socket = None
            print("Verbindung getrennt.")
    
    def send_command(self, command: bytes) -> bytes:
        """
        Sendet einen Befehl und wartet auf Antwort
        
        Args:
            command: Zu sendender Befehl
            
        Returns:
            bytes: Antwort vom Server
        """
        if not self.socket:
            raise RuntimeError("Nicht verbunden")
            
        self.socket.send(command)
        
        # Auf Antwort warten
        response = self.socket.recv(1024)
        return response
    
    def program_hex_file(self, hex_file_path: str) -> bool:
        """
        Programmiert eine HEX-Datei ins EEPROM
        
        Args:
            hex_file_path: Pfad zur HEX-Datei
            
        Returns:
            bool: True wenn erfolgreich
        """
        try:
            # HEX-Datei lesen
            with open(hex_file_path, 'r') as f:
                hex_content = f.read().strip()
            
            print(f"Lade HEX-Datei: {hex_file_path}")
            print(f"Größe: {len(hex_content)} Zeichen")
            
            # Befehl zusammenstellen
            command = f"PROGRAM_HEX:{hex_content}".encode('utf-8')
            
            print("Sende HEX-Daten an EEPROM...")
            
            # Befehl senden
            response = self.send_command(command)
            
            # Antwort auswerten
            if response == b"PROGRAM_HEX_OK":
                print("✓ EEPROM erfolgreich programmiert!")
                return True
            else:
                print(f"✗ EEPROM-Programmierung fehlgeschlagen: {response}")
                return False
                
        except Exception as e:
            print(f"Fehler beim Programmieren: {e}")
            return False
    
    def write_eeprom_data(self, address: int, data: bytes) -> bool:
        """
        Schreibt Binärdaten direkt ins EEPROM
        
        Args:
            address: Start-Adresse
            data: Zu schreibende Daten
            
        Returns:
            bool: True wenn erfolgreich
        """
        try:
            hex_data = data.hex().upper()
            command = f"EEPROM_WRITE:{address:04X}:{hex_data}".encode('utf-8')
            
            print(f"Schreibe {len(data)} Bytes ab Adresse 0x{address:04X}")
            
            response = self.send_command(command)
            
            if response == b"EEPROM_WRITE_OK":
                print("✓ EEPROM-Schreibvorgang erfolgreich!")
                return True
            else:
                print(f"✗ EEPROM-Schreibvorgang fehlgeschlagen: {response}")
                return False
                
        except Exception as e:
            print(f"Fehler beim EEPROM-Schreiben: {e}")
            return False

    def program_binary_file(self, binary_file_path):
        """
        Programmiert eine Binär-Datei (E2PROM.bin) ins EEPROM

        Args:
            binary_file_path: Pfad zur Binär-Datei

        Returns:
            bool: True wenn erfolgreich
        """
        try:
            # Binär-Datei lesen
            with open(binary_file_path, 'rb') as f:
                binary_data = f.read()

            print(f"Lade Binär-Datei: {binary_file_path}")
            print(f"Größe: {len(binary_data)} Bytes")

            # Direkt als EEPROM_WRITE mit Adresse 0x0000
            success = self.write_eeprom_data(0x0000, binary_data)

            if success:
                print("✓ E2PROM.bin erfolgreich programmiert!")
                return True
            else:
                print("✗ E2PROM.bin Programmierung fehlgeschlagen!")
                return False

        except Exception as e:
            print(f"Fehler beim Programmieren der Binär-Datei: {e}")
            return False

    def upload_to_ram(self, hex_file_path):
        """
        Lädt HEX-Datei direkt in DSP RAM (ohne EEPROM)

        Args:
            hex_file_path: Pfad zur HEX-Datei

        Returns:
            bool: True wenn erfolgreich
        """
        try:
            print(f"Lade HEX-Datei für RAM-Upload: {hex_file_path}")

            # HEX-Datei lesen und parsen
            with open(hex_file_path, 'r') as f:
                hex_content = f.read().strip()

            # Befehl zusammenstellen
            command = f"RAM_UPLOAD:{hex_content}".encode('utf-8')

            print("Sende Daten für RAM-Upload...")

            # Befehl senden
            response = self.send_command(command)

            # Antwort auswerten
            if response == b"RAM_UPLOAD_OK":
                print("✓ RAM-Upload erfolgreich!")
                return True
            else:
                print(f"✗ RAM-Upload fehlgeschlagen: {response}")
                return False

        except Exception as e:
            print(f"Fehler beim RAM-Upload: {e}")
            return False

    def read_eeprom_to_file(self, output_file):
        """
        Liest EEPROM aus und speichert in Datei

        Args:
            output_file: Pfad zur Ausgabedatei

        Returns:
            bool: True wenn erfolgreich
        """
        try:
            print(f"Lese EEPROM aus...")

            # Befehl zusammenstellen
            command = b"READ_EEPROM:4096"  # 4KB lesen

            # Befehl senden
            response = self.send_command(command)

            if response.startswith(b"EEPROM_DATA:"):
                # Daten extrahieren
                eeprom_data = response[12:]  # "EEPROM_DATA:" = 12 Zeichen

                # In Datei speichern
                with open(output_file, 'wb') as f:
                    f.write(eeprom_data)

                print(f"✓ EEPROM ausgelesen: {len(eeprom_data)} Bytes → {output_file}")
                return True
            else:
                print(f"✗ EEPROM-Auslesen fehlgeschlagen: {response}")
                return False

        except Exception as e:
            print(f"Fehler beim EEPROM-Auslesen: {e}")
            return False

    def scan_i2c_bus(self):
        """
        Scannt I2C Bus nach Geräten

        Returns:
            bool: True wenn erfolgreich
        """
        try:
            print("Scanne I2C Bus...")

            # Befehl zusammenstellen
            command = b"I2C_SCAN"

            # Befehl senden
            response = self.send_command(command)

            if response.startswith(b"I2C_DEVICES:"):
                # Geräte-Liste extrahieren
                devices = response[12:].decode('utf-8')
                print(f"✓ I2C Geräte gefunden: {devices}")
                return True
            else:
                print(f"✗ I2C-Scan fehlgeschlagen: {response}")
                return False

        except Exception as e:
            print(f"Fehler beim I2C-Scan: {e}")
            return False

    def reset_dsp(self) -> bool:
        """
        Sendet DSP-Reset-Befehl
        
        Returns:
            bool: True wenn erfolgreich
        """
        try:
            command = b"RESET_DSP"
            response = self.send_command(command)
            print("DSP Reset gesendet")
            return True
        except Exception as e:
            print(f"Fehler beim DSP-Reset: {e}")
            return False


def main():
    """Hauptfunktion für Command-Line Interface"""
    parser = argparse.ArgumentParser(description="EEPROM Programmer für ESP32 TCPI Adapter")
    parser.add_argument("--host", default="*************", help="IP-Adresse des ESP32")
    parser.add_argument("--port", type=int, default=8086, help="TCP-Port")
    parser.add_argument("--hex-file", help="Pfad zur Sigma Studio HEX-Datei")
    parser.add_argument("--binary-file", help="Pfad zur Sigma Studio E2PROM.bin Datei")
    parser.add_argument("--ram-upload", help="Pfad zur Sigma Studio HEX-Datei für RAM-Upload")
    parser.add_argument("--eeprom-hex", help="Pfad zur HEX-Datei für EEPROM-Programmierung (Alternative zu --binary-file)")
    parser.add_argument("--read-eeprom", help="EEPROM auslesen und in Datei speichern")
    parser.add_argument("--i2c-scan", action="store_true", help="I2C Bus scannen")
    parser.add_argument("--reset-dsp", action="store_true", help="DSP nach Programmierung zurücksetzen")
    parser.add_argument("--test", action="store_true", help="Test-Modus mit Dummy-Daten")
    
    args = parser.parse_args()
    
    # Programmer initialisieren
    programmer = EEPROMProgrammer(args.host, args.port)
    
    try:
        # Verbinden
        if not programmer.connect():
            sys.exit(1)
        
        success = True
        
        # Test-Modus
        if args.test:
            print("Test-Modus: Schreibe Test-Daten...")
            test_data = b'\x01\x02\x03\x04\x05\x06\x07\x08'
            success = programmer.write_eeprom_data(0x0000, test_data)
        
        # HEX-Datei programmieren
        elif args.hex_file:
            if not Path(args.hex_file).exists():
                print(f"HEX-Datei nicht gefunden: {args.hex_file}")
                sys.exit(1)

            success = programmer.program_hex_file(args.hex_file)

        # Binär-Datei programmieren (E2PROM.bin)
        elif args.binary_file:
            if not Path(args.binary_file).exists():
                print(f"Binär-Datei nicht gefunden: {args.binary_file}")
                sys.exit(1)

            success = programmer.program_binary_file(args.binary_file)

        # RAM-Upload (direkt in DSP RAM)
        elif args.ram_upload:
            if not Path(args.ram_upload).exists():
                print(f"HEX-Datei nicht gefunden: {args.ram_upload}")
                sys.exit(1)

            success = programmer.upload_to_ram(args.ram_upload)

        # EEPROM-Programmierung mit HEX-Datei
        elif args.eeprom_hex:
            if not Path(args.eeprom_hex).exists():
                print(f"HEX-Datei nicht gefunden: {args.eeprom_hex}")
                sys.exit(1)

            success = programmer.program_hex_file(args.eeprom_hex)

        # EEPROM auslesen
        elif args.read_eeprom:
            success = programmer.read_eeprom_to_file(args.read_eeprom)

        # I2C Bus scannen
        elif args.i2c_scan:
            success = programmer.scan_i2c_bus()
        
        else:
            print("Keine Aktion angegeben. Verwende:")
            print("  --hex-file <file>      : HEX-Datei ins EEPROM (alt)")
            print("  --binary-file <file>   : E2PROM.bin ins EEPROM (empfohlen)")
            print("  --eeprom-hex <file>    : HEX-Datei ins EEPROM (langsam)")
            print("  --ram-upload <file>    : HEX-Datei direkt in DSP RAM (schnell)")
            print("  --read-eeprom <file>   : EEPROM auslesen")
            print("  --i2c-scan             : I2C Bus scannen")
            print("  --test                 : Test-Modus")
            sys.exit(1)
        
        # DSP Reset
        if success and args.reset_dsp:
            print("Setze DSP zurück...")
            programmer.reset_dsp()
            time.sleep(1)
        
        if success:
            print("\n✓ Alle Operationen erfolgreich abgeschlossen!")
        else:
            print("\n✗ Fehler bei der Ausführung!")
            sys.exit(1)
    
    finally:
        programmer.disconnect()


if __name__ == "__main__":
    main()
