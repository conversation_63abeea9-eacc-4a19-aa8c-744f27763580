/*
 * File:           I:\Coden\Audio\EspSigmatTcpiAdapter\sigma_test_IC_2_REG.h
 *
 * Created:        Saturday, June 14, 2025 4:20:02 AM
 * Description:    2x2test:IC 2 control register definitions.
 *
 * This software is distributed in the hope that it will be useful,
 * but is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 *
 * This software may only be used to program products purchased from
 * Analog Devices for incorporation by you into audio products that
 * are intended for resale to audio product end users. This software
 * may not be distributed whole or in any part to third parties.
 *
 * Copyright ©2025 Analog Devices, Inc. All rights reserved.
 */
#ifndef __SIGMA_TEST_IC_2_REG_H__
#define __SIGMA_TEST_IC_2_REG_H__


/* InterfaceRegister0  - Registers (IC 2) */
#define REG_INTERFACEREGISTER0_IC_2_ADDR          0x800
#define REG_INTERFACEREGISTER0_IC_2_BYTE          4
#define REG_INTERFACEREGISTER0_IC_2_VALUE         0x0

/* InterfaceRegister1  - Registers (IC 2) */
#define REG_INTERFACEREGISTER1_IC_2_ADDR          0x801
#define REG_INTERFACEREGISTER1_IC_2_BYTE          4
#define REG_INTERFACEREGISTER1_IC_2_VALUE         0x0

/* InterfaceRegister2  - Registers (IC 2) */
#define REG_INTERFACEREGISTER2_IC_2_ADDR          0x802
#define REG_INTERFACEREGISTER2_IC_2_BYTE          4
#define REG_INTERFACEREGISTER2_IC_2_VALUE         0x0

/* InterfaceRegister3  - Registers (IC 2) */
#define REG_INTERFACEREGISTER3_IC_2_ADDR          0x803
#define REG_INTERFACEREGISTER3_IC_2_BYTE          4
#define REG_INTERFACEREGISTER3_IC_2_VALUE         0x0

/* InterfaceRegister4  - Registers (IC 2) */
#define REG_INTERFACEREGISTER4_IC_2_ADDR          0x804
#define REG_INTERFACEREGISTER4_IC_2_BYTE          4
#define REG_INTERFACEREGISTER4_IC_2_VALUE         0x0

/* InterfaceRegister5  - Registers (IC 2) */
#define REG_INTERFACEREGISTER5_IC_2_ADDR          0x805
#define REG_INTERFACEREGISTER5_IC_2_BYTE          4
#define REG_INTERFACEREGISTER5_IC_2_VALUE         0x0

/* InterfaceRegister6  - Registers (IC 2) */
#define REG_INTERFACEREGISTER6_IC_2_ADDR          0x806
#define REG_INTERFACEREGISTER6_IC_2_BYTE          4
#define REG_INTERFACEREGISTER6_IC_2_VALUE         0x0

/* InterfaceRegister7  - Registers (IC 2) */
#define REG_INTERFACEREGISTER7_IC_2_ADDR          0x807
#define REG_INTERFACEREGISTER7_IC_2_BYTE          4
#define REG_INTERFACEREGISTER7_IC_2_VALUE         0x0

/* GpioAllRegister  - Registers (IC 2) */
#define REG_GPIOALLREGISTER_IC_2_ADDR             0x808
#define REG_GPIOALLREGISTER_IC_2_BYTE             2
#define REG_GPIOALLREGISTER_IC_2_VALUE            0x0

/* Adc0  - Registers (IC 2) */
#define REG_ADC0_IC_2_ADDR                        0x809
#define REG_ADC0_IC_2_BYTE                        1
#define REG_ADC0_IC_2_VALUE                       0x0

/* Adc1  - Registers (IC 2) */
#define REG_ADC1_IC_2_ADDR                        0x80A
#define REG_ADC1_IC_2_BYTE                        1
#define REG_ADC1_IC_2_VALUE                       0x0

/* Adc2  - Registers (IC 2) */
#define REG_ADC2_IC_2_ADDR                        0x80B
#define REG_ADC2_IC_2_BYTE                        1
#define REG_ADC2_IC_2_VALUE                       0x0

/* Adc3  - Registers (IC 2) */
#define REG_ADC3_IC_2_ADDR                        0x80C
#define REG_ADC3_IC_2_BYTE                        1
#define REG_ADC3_IC_2_VALUE                       0x0

/* CoreRegister  - Registers (IC 2) */
#define REG_COREREGISTER_IC_2_ADDR                0x81C
#define REG_COREREGISTER_IC_2_BYTE                2
#define REG_COREREGISTER_IC_2_VALUE               0x1C

/* RAMRegister  - Registers (IC 2) */
#define REG_RAMREGISTER_IC_2_ADDR                 0x81D
#define REG_RAMREGISTER_IC_2_BYTE                 1
#define REG_RAMREGISTER_IC_2_VALUE                0x8

/* SerialOutRegister1  - Registers (IC 2) */
#define REG_SERIALOUTREGISTER1_IC_2_ADDR          0x81E
#define REG_SERIALOUTREGISTER1_IC_2_BYTE          2
#define REG_SERIALOUTREGISTER1_IC_2_VALUE         0x0

/* SerialInputRegister  - Registers (IC 2) */
#define REG_SERIALINPUTREGISTER_IC_2_ADDR         0x81F
#define REG_SERIALINPUTREGISTER_IC_2_BYTE         1
#define REG_SERIALINPUTREGISTER_IC_2_VALUE        0x0

/* MpCfg0  - Registers (IC 2) */
#define REG_MPCFG0_IC_2_ADDR                      0x820
#define REG_MPCFG0_IC_2_BYTE                      3
#define REG_MPCFG0_IC_2_VALUE                     0xFF00

/* MpCfg1  - Registers (IC 2) */
#define REG_MPCFG1_IC_2_ADDR                      0x821
#define REG_MPCFG1_IC_2_BYTE                      3
#define REG_MPCFG1_IC_2_VALUE                     0xFF00

/* AnalogPowerDownRegister  - Registers (IC 2) */
#define REG_ANALOGPOWERDOWNREGISTER_IC_2_ADDR     0x822
#define REG_ANALOGPOWERDOWNREGISTER_IC_2_BYTE     2
#define REG_ANALOGPOWERDOWNREGISTER_IC_2_VALUE    0x0

/* TestRegister  - Registers (IC 2) */
#define REG_TESTREGISTER_IC_2_ADDR                0x823
#define REG_TESTREGISTER_IC_2_BYTE                2
#define REG_TESTREGISTER_IC_2_VALUE               0x0

/* AnalogInterfaceRegister0  - Registers (IC 2) */
#define REG_ANALOGINTERFACEREGISTER0_IC_2_ADDR    0x824
#define REG_ANALOGINTERFACEREGISTER0_IC_2_BYTE    2
#define REG_ANALOGINTERFACEREGISTER0_IC_2_VALUE   0x8000

/* AnalogInterfaceRegister1  - Registers (IC 2) */
#define REG_ANALOGINTERFACEREGISTER1_IC_2_ADDR    0x825
#define REG_ANALOGINTERFACEREGISTER1_IC_2_BYTE    2
#define REG_ANALOGINTERFACEREGISTER1_IC_2_VALUE   0x0

/* AnalogInterfaceRegister2  - Registers (IC 2) */
#define REG_ANALOGINTERFACEREGISTER2_IC_2_ADDR    0x826
#define REG_ANALOGINTERFACEREGISTER2_IC_2_BYTE    2
#define REG_ANALOGINTERFACEREGISTER2_IC_2_VALUE   0x0

/* AnalogInterfaceRegister3  - Registers (IC 2) */
#define REG_ANALOGINTERFACEREGISTER3_IC_2_ADDR    0x827
#define REG_ANALOGINTERFACEREGISTER3_IC_2_BYTE    2
#define REG_ANALOGINTERFACEREGISTER3_IC_2_VALUE   0x1


/*
 *
 * Control register's field descriptions
 *
 */

/* InterfaceRegister0 (IC 2) */
#define R0_INTERFACEREGISTER0_IC_2                0x00000000 /* 00000000000000000000000000000000b	[31:0] */
#define R0_INTERFACEREGISTER0_IC_2_MASK           0xFFFFFFFF
#define R0_INTERFACEREGISTER0_IC_2_SHIFT          0

/* InterfaceRegister1 (IC 2) */
#define R1_INTERFACEREGISTER1_IC_2                0x00000000 /* 00000000000000000000000000000000b	[31:0] */
#define R1_INTERFACEREGISTER1_IC_2_MASK           0xFFFFFFFF
#define R1_INTERFACEREGISTER1_IC_2_SHIFT          0

/* InterfaceRegister2 (IC 2) */
#define R2_INTERFACEREGISTER2_IC_2                0x00000000 /* 00000000000000000000000000000000b	[31:0] */
#define R2_INTERFACEREGISTER2_IC_2_MASK           0xFFFFFFFF
#define R2_INTERFACEREGISTER2_IC_2_SHIFT          0

/* InterfaceRegister3 (IC 2) */
#define R3_INTERFACEREGISTER3_IC_2                0x00000000 /* 00000000000000000000000000000000b	[31:0] */
#define R3_INTERFACEREGISTER3_IC_2_MASK           0xFFFFFFFF
#define R3_INTERFACEREGISTER3_IC_2_SHIFT          0

/* InterfaceRegister4 (IC 2) */
#define R4_INTERFACEREGISTER4_IC_2                0x00000000 /* 00000000000000000000000000000000b	[31:0] */
#define R4_INTERFACEREGISTER4_IC_2_MASK           0xFFFFFFFF
#define R4_INTERFACEREGISTER4_IC_2_SHIFT          0

/* InterfaceRegister5 (IC 2) */
#define R5_INTERFACEREGISTER5_IC_2                0x00000000 /* 00000000000000000000000000000000b	[31:0] */
#define R5_INTERFACEREGISTER5_IC_2_MASK           0xFFFFFFFF
#define R5_INTERFACEREGISTER5_IC_2_SHIFT          0

/* InterfaceRegister6 (IC 2) */
#define R6_INTERFACEREGISTER6_IC_2                0x00000000 /* 00000000000000000000000000000000b	[31:0] */
#define R6_INTERFACEREGISTER6_IC_2_MASK           0xFFFFFFFF
#define R6_INTERFACEREGISTER6_IC_2_SHIFT          0

/* InterfaceRegister7 (IC 2) */
#define R7_INTERFACEREGISTER7_IC_2                0x00000000 /* 00000000000000000000000000000000b	[31:0] */
#define R7_INTERFACEREGISTER7_IC_2_MASK           0xFFFFFFFF
#define R7_INTERFACEREGISTER7_IC_2_SHIFT          0

/* GpioAllRegister (IC 2) */
#define R8_PIN0_IC_2                              0x0    /* 0b	[0] */
#define R8_PIN1_IC_2                              0x0    /* 0b	[1] */
#define R8_PIN2_IC_2                              0x0    /* 0b	[2] */
#define R8_PIN3_IC_2                              0x0    /* 0b	[3] */
#define R8_PIN4_IC_2                              0x0    /* 0b	[4] */
#define R8_PIN5_IC_2                              0x0    /* 0b	[5] */
#define R8_PIN6_IC_2                              0x0    /* 0b	[6] */
#define R8_PIN7_IC_2                              0x0    /* 0b	[7] */
#define R8_PIN8_IC_2                              0x0    /* 0b	[8] */
#define R8_PIN9_IC_2                              0x0    /* 0b	[9] */
#define R8_PIN10_IC_2                             0x0    /* 0b	[10] */
#define R8_PIN11_IC_2                             0x0    /* 0b	[11] */
#define R8_PIN0_IC_2_MASK                         0x1
#define R8_PIN0_IC_2_SHIFT                        0
#define R8_PIN1_IC_2_MASK                         0x2
#define R8_PIN1_IC_2_SHIFT                        1
#define R8_PIN2_IC_2_MASK                         0x4
#define R8_PIN2_IC_2_SHIFT                        2
#define R8_PIN3_IC_2_MASK                         0x8
#define R8_PIN3_IC_2_SHIFT                        3
#define R8_PIN4_IC_2_MASK                         0x10
#define R8_PIN4_IC_2_SHIFT                        4
#define R8_PIN5_IC_2_MASK                         0x20
#define R8_PIN5_IC_2_SHIFT                        5
#define R8_PIN6_IC_2_MASK                         0x40
#define R8_PIN6_IC_2_SHIFT                        6
#define R8_PIN7_IC_2_MASK                         0x80
#define R8_PIN7_IC_2_SHIFT                        7
#define R8_PIN8_IC_2_MASK                         0x100
#define R8_PIN8_IC_2_SHIFT                        8
#define R8_PIN9_IC_2_MASK                         0x200
#define R8_PIN9_IC_2_SHIFT                        9
#define R8_PIN10_IC_2_MASK                        0x400
#define R8_PIN10_IC_2_SHIFT                       10
#define R8_PIN11_IC_2_MASK                        0x800
#define R8_PIN11_IC_2_SHIFT                       11

/* Adc0 (IC 2) */
#define R9_ADC0_IC_2                              0x00   /* 00000000b	[7:0] */
#define R9_ADC0_IC_2_MASK                         0xFF
#define R9_ADC0_IC_2_SHIFT                        0

/* Adc1 (IC 2) */
#define R10_ADC1_IC_2                             0x00   /* 00000000b	[7:0] */
#define R10_ADC1_IC_2_MASK                        0xFF
#define R10_ADC1_IC_2_SHIFT                       0

/* Adc2 (IC 2) */
#define R11_ADC2_IC_2                             0x00   /* 00000000b	[7:0] */
#define R11_ADC2_IC_2_MASK                        0xFF
#define R11_ADC2_IC_2_SHIFT                       0

/* Adc3 (IC 2) */
#define R12_ADC3_IC_2                             0x00   /* 00000000b	[7:0] */
#define R12_ADC3_IC_2_MASK                        0xFF
#define R12_ADC3_IC_2_SHIFT                       0

/* CoreRegister (IC 2) */
#define R13_PROGRAM_LENGTH_IC_2                   0x0    /* 00b	[1:0] */
#define R13_REGISTER_ZERO_IC_2                    0x1    /* 1b	[2] */
#define R13_MUTE_DAC_IC_2                         0x1    /* 1b	[3] */
#define R13_MUTE_ADC_IC_2                         0x1    /* 1b	[4] */
#define R13_SAFELOAD_IC_2                         0x0    /* 0b	[5] */
#define R13_WRITESPI_INTERFACEREG_IC_2            0x0    /* 0b	[6] */
#define R13_WRITESPI_GPIO_IC_2                    0x0    /* 0b	[7] */
#define R13_WRITESPI_ADC_IC_2                     0x0    /* 0b	[8] */
#define R13_MUTE_SERIALIN_IC_2                    0x0    /* 0b	[9] */
#define R13_GPIO_DEBOUNCE_IC_2                    0x0    /* 00b	[11:10] */
#define R13_EXTMEM_SPEED_IC_2                     0x0    /* 00b	[13:12] */
#define R13_PROGRAM_LENGTH_IC_2_MASK              0x3
#define R13_PROGRAM_LENGTH_IC_2_SHIFT             0
#define R13_REGISTER_ZERO_IC_2_MASK               0x4
#define R13_REGISTER_ZERO_IC_2_SHIFT              2
#define R13_MUTE_DAC_IC_2_MASK                    0x8
#define R13_MUTE_DAC_IC_2_SHIFT                   3
#define R13_MUTE_ADC_IC_2_MASK                    0x10
#define R13_MUTE_ADC_IC_2_SHIFT                   4
#define R13_SAFELOAD_IC_2_MASK                    0x20
#define R13_SAFELOAD_IC_2_SHIFT                   5
#define R13_WRITESPI_INTERFACEREG_IC_2_MASK       0x40
#define R13_WRITESPI_INTERFACEREG_IC_2_SHIFT      6
#define R13_WRITESPI_GPIO_IC_2_MASK               0x80
#define R13_WRITESPI_GPIO_IC_2_SHIFT              7
#define R13_WRITESPI_ADC_IC_2_MASK                0x100
#define R13_WRITESPI_ADC_IC_2_SHIFT               8
#define R13_MUTE_SERIALIN_IC_2_MASK               0x200
#define R13_MUTE_SERIALIN_IC_2_SHIFT              9
#define R13_GPIO_DEBOUNCE_IC_2_MASK               0xC00
#define R13_GPIO_DEBOUNCE_IC_2_SHIFT              10
#define R13_EXTMEM_SPEED_IC_2_MASK                0x3000
#define R13_EXTMEM_SPEED_IC_2_SHIFT               12

/* RAMRegister (IC 2) */
#define R14_RAM_MODULO_IC_2                       0x8    /* 1000b	[3:0] */
#define R14_RAM_MODULO_IC_2_MASK                  0xF
#define R14_RAM_MODULO_IC_2_SHIFT                 0

/* SerialOutRegister1 (IC 2) */
#define R15_WORDLENGTH_IC_2                       0x0    /* 00b	[1:0] */
#define R15_MSB_POS_IC_2                          0x0    /* 000b	[4:2] */
#define R15_TDM_IC_2                              0x0    /* 0b	[5] */
#define R15_FRAMESYNC_TYPE_IC_2                   0x0    /* 0b	[6] */
#define R15_LRCLK_FREQ_IC_2                       0x0    /* 00b	[8:7] */
#define R15_BCLK_FREQ_IC_2                        0x0    /* 00b	[10:9] */
#define R15_MASTER_SLAVE_IC_2                     0x0    /* 0b	[11] */
#define R15_BCLK_POLARITY_IC_2                    0x0    /* 0b	[12] */
#define R15_LRCLK_POLARITY_IC_2                   0x0    /* 0b	[13] */
#define R15_WORDLENGTH_IC_2_MASK                  0x3
#define R15_WORDLENGTH_IC_2_SHIFT                 0
#define R15_MSB_POS_IC_2_MASK                     0x1C
#define R15_MSB_POS_IC_2_SHIFT                    2
#define R15_TDM_IC_2_MASK                         0x20
#define R15_TDM_IC_2_SHIFT                        5
#define R15_FRAMESYNC_TYPE_IC_2_MASK              0x40
#define R15_FRAMESYNC_TYPE_IC_2_SHIFT             6
#define R15_LRCLK_FREQ_IC_2_MASK                  0x180
#define R15_LRCLK_FREQ_IC_2_SHIFT                 7
#define R15_BCLK_FREQ_IC_2_MASK                   0x600
#define R15_BCLK_FREQ_IC_2_SHIFT                  9
#define R15_MASTER_SLAVE_IC_2_MASK                0x800
#define R15_MASTER_SLAVE_IC_2_SHIFT               11
#define R15_BCLK_POLARITY_IC_2_MASK               0x1000
#define R15_BCLK_POLARITY_IC_2_SHIFT              12
#define R15_LRCLK_POLARITY_IC_2_MASK              0x2000
#define R15_LRCLK_POLARITY_IC_2_SHIFT             13

/* SerialInputRegister (IC 2) */
#define R16_INPUT_MODE_IC_2                       0x0    /* 000b	[2:0] */
#define R16_BCLK_POLARITY_IC_2                    0x0    /* 0b	[3] */
#define R16_LRCLK_POLARITY_IC_2                   0x0    /* 0b	[4] */
#define R16_INPUT_MODE_IC_2_MASK                  0x7
#define R16_INPUT_MODE_IC_2_SHIFT                 0
#define R16_BCLK_POLARITY_IC_2_MASK               0x8
#define R16_BCLK_POLARITY_IC_2_SHIFT              3
#define R16_LRCLK_POLARITY_IC_2_MASK              0x10
#define R16_LRCLK_POLARITY_IC_2_SHIFT             4

/* MpCfg0 (IC 2) */
#define R17_MFSELECT0_IC_2                        0x0    /* 000b	[2:0] */
#define R17_MFINVERT0_IC_2                        0x0    /* 0b	[3] */
#define R17_MFSELECT1_IC_2                        0x0    /* 000b	[6:4] */
#define R17_MFINVERT1_IC_2                        0x0    /* 0b	[7] */
#define R17_MFSELECT2_IC_2                        0x7    /* 111b	[10:8] */
#define R17_MFINVERT2_IC_2                        0x1    /* 1b	[11] */
#define R17_MFSELECT3_IC_2                        0x7    /* 111b	[14:12] */
#define R17_MFINVERT3_IC_2                        0x1    /* 1b	[15] */
#define R17_MFSELECT4_IC_2                        0x0    /* 000b	[18:16] */
#define R17_MFINVERT4_IC_2                        0x0    /* 0b	[19] */
#define R17_MFSELECT5_IC_2                        0x0    /* 000b	[22:20] */
#define R17_MFINVERT5_IC_2                        0x0    /* 0b	[23] */
#define R17_MFSELECT0_IC_2_MASK                   0x7
#define R17_MFSELECT0_IC_2_SHIFT                  0
#define R17_MFINVERT0_IC_2_MASK                   0x8
#define R17_MFINVERT0_IC_2_SHIFT                  3
#define R17_MFSELECT1_IC_2_MASK                   0x70
#define R17_MFSELECT1_IC_2_SHIFT                  4
#define R17_MFINVERT1_IC_2_MASK                   0x80
#define R17_MFINVERT1_IC_2_SHIFT                  7
#define R17_MFSELECT2_IC_2_MASK                   0x700
#define R17_MFSELECT2_IC_2_SHIFT                  8
#define R17_MFINVERT2_IC_2_MASK                   0x800
#define R17_MFINVERT2_IC_2_SHIFT                  11
#define R17_MFSELECT3_IC_2_MASK                   0x7000
#define R17_MFSELECT3_IC_2_SHIFT                  12
#define R17_MFINVERT3_IC_2_MASK                   0x8000
#define R17_MFINVERT3_IC_2_SHIFT                  15
#define R17_MFSELECT4_IC_2_MASK                   0x70000
#define R17_MFSELECT4_IC_2_SHIFT                  16
#define R17_MFINVERT4_IC_2_MASK                   0x80000
#define R17_MFINVERT4_IC_2_SHIFT                  19
#define R17_MFSELECT5_IC_2_MASK                   0x700000
#define R17_MFSELECT5_IC_2_SHIFT                  20
#define R17_MFINVERT5_IC_2_MASK                   0x800000
#define R17_MFINVERT5_IC_2_SHIFT                  23

/* MpCfg1 (IC 2) */
#define R18_MFSELECT6_IC_2                        0x0    /* 000b	[2:0] */
#define R18_MFINVERT6_IC_2                        0x0    /* 0b	[3] */
#define R18_MFSELECT7_IC_2                        0x0    /* 000b	[6:4] */
#define R18_MFINVERT7_IC_2                        0x0    /* 0b	[7] */
#define R18_MFSELECT8_IC_2                        0x7    /* 111b	[10:8] */
#define R18_MFINVERT8_IC_2                        0x1    /* 1b	[11] */
#define R18_MFSELECT9_IC_2                        0x7    /* 111b	[14:12] */
#define R18_MFINVERT9_IC_2                        0x1    /* 1b	[15] */
#define R18_MFSELECT10_IC_2                       0x0    /* 000b	[18:16] */
#define R18_MFINVERT10_IC_2                       0x0    /* 0b	[19] */
#define R18_MFSELECT11_IC_2                       0x0    /* 000b	[22:20] */
#define R18_MFINVERT11_IC_2                       0x0    /* 0b	[23] */
#define R18_MFSELECT6_IC_2_MASK                   0x7
#define R18_MFSELECT6_IC_2_SHIFT                  0
#define R18_MFINVERT6_IC_2_MASK                   0x8
#define R18_MFINVERT6_IC_2_SHIFT                  3
#define R18_MFSELECT7_IC_2_MASK                   0x70
#define R18_MFSELECT7_IC_2_SHIFT                  4
#define R18_MFINVERT7_IC_2_MASK                   0x80
#define R18_MFINVERT7_IC_2_SHIFT                  7
#define R18_MFSELECT8_IC_2_MASK                   0x700
#define R18_MFSELECT8_IC_2_SHIFT                  8
#define R18_MFINVERT8_IC_2_MASK                   0x800
#define R18_MFINVERT8_IC_2_SHIFT                  11
#define R18_MFSELECT9_IC_2_MASK                   0x7000
#define R18_MFSELECT9_IC_2_SHIFT                  12
#define R18_MFINVERT9_IC_2_MASK                   0x8000
#define R18_MFINVERT9_IC_2_SHIFT                  15
#define R18_MFSELECT10_IC_2_MASK                  0x70000
#define R18_MFSELECT10_IC_2_SHIFT                 16
#define R18_MFINVERT10_IC_2_MASK                  0x80000
#define R18_MFINVERT10_IC_2_SHIFT                 19
#define R18_MFSELECT11_IC_2_MASK                  0x700000
#define R18_MFSELECT11_IC_2_SHIFT                 20
#define R18_MFINVERT11_IC_2_MASK                  0x800000
#define R18_MFINVERT11_IC_2_SHIFT                 23

/* AnalogPowerDownRegister (IC 2) */
#define R19_DAC3_POWERDOWN_IC_2                   0x0    /* 0b	[0] */
#define R19_DAC2_POWERDOWN_IC_2                   0x0    /* 0b	[1] */
#define R19_DAC1_POWERDOWN_IC_2                   0x0    /* 0b	[2] */
#define R19_DAC0_POWERDOWN_IC_2                   0x0    /* 0b	[3] */
#define R19_DACS_RESET_IC_2                       0x0    /* 0b	[4] */
#define R19_REF_CORE_POWERDOWN_IC_2               0x0    /* 0b	[5] */
#define R19_REF_BUF_POWERDOWN_IC_2                0x0    /* 0b	[6] */
#define R19_ADCS_POWERDOWN_IC_2                   0x0    /* 0b	[7] */
#define R19_AUX_ADC_FILTER_IC_2                   0x0    /* 000b	[10:8] */
#define R19_DAC3_POWERDOWN_IC_2_MASK              0x1
#define R19_DAC3_POWERDOWN_IC_2_SHIFT             0
#define R19_DAC2_POWERDOWN_IC_2_MASK              0x2
#define R19_DAC2_POWERDOWN_IC_2_SHIFT             1
#define R19_DAC1_POWERDOWN_IC_2_MASK              0x4
#define R19_DAC1_POWERDOWN_IC_2_SHIFT             2
#define R19_DAC0_POWERDOWN_IC_2_MASK              0x8
#define R19_DAC0_POWERDOWN_IC_2_SHIFT             3
#define R19_DACS_RESET_IC_2_MASK                  0x10
#define R19_DACS_RESET_IC_2_SHIFT                 4
#define R19_REF_CORE_POWERDOWN_IC_2_MASK          0x20
#define R19_REF_CORE_POWERDOWN_IC_2_SHIFT         5
#define R19_REF_BUF_POWERDOWN_IC_2_MASK           0x40
#define R19_REF_BUF_POWERDOWN_IC_2_SHIFT          6
#define R19_ADCS_POWERDOWN_IC_2_MASK              0x80
#define R19_ADCS_POWERDOWN_IC_2_SHIFT             7
#define R19_AUX_ADC_FILTER_IC_2_MASK              0x700
#define R19_AUX_ADC_FILTER_IC_2_SHIFT             8

/* TestRegister (IC 2) */
#define R20_ADCCLOCKDELAY_IC_2                    0x0    /* 000b	[2:0] */
#define R20_ADCSYNCFILTL_SDATAA_D_IC_2            0x0    /* 0b	[3] */
#define R20_ADCSYNCFILTR_SDATAA_D_IC_2            0x0    /* 0b	[4] */
#define R20_SPIKEFILT_SEROUT0_1_IC_2              0x0    /* 0b	[5] */
#define R20_UNUSED_IC_2                           0x00   /* 0000000000b	[15:6] */
#define R20_ADCCLOCKDELAY_IC_2_MASK               0x7
#define R20_ADCCLOCKDELAY_IC_2_SHIFT              0
#define R20_ADCSYNCFILTL_SDATAA_D_IC_2_MASK       0x8
#define R20_ADCSYNCFILTL_SDATAA_D_IC_2_SHIFT      3
#define R20_ADCSYNCFILTR_SDATAA_D_IC_2_MASK       0x10
#define R20_ADCSYNCFILTR_SDATAA_D_IC_2_SHIFT      4
#define R20_SPIKEFILT_SEROUT0_1_IC_2_MASK         0x20
#define R20_SPIKEFILT_SEROUT0_1_IC_2_SHIFT        5
#define R20_UNUSED_IC_2_MASK                      0xFFC0
#define R20_UNUSED_IC_2_SHIFT                     6

/* AnalogInterfaceRegister0 (IC 2) */
#define R21_CHOP_ENB_INT_AMP_IC_2                 0x0    /* 0b	[0] */
#define R21_PD_INT_AMP_IC_2                       0x0    /* 0b	[1] */
#define R21_ADC_NON_OVERLAP_ADJ_IC_2              0x0    /* 0b	[2] */
#define R21_ADC_I_SEL_IC_2                        0x0    /* 00000b	[7:3] */
#define R21_ADC_CLK_ADJ_IC_2                      0x0    /* 000b	[10:8] */
#define R21_LCHOP_PMOS_CTRL_IC_2                  0x0    /* 00b	[12:11] */
#define R21_LCHOP_AMP_ENB_IC_2                    0x0    /* 0b	[13] */
#define R21_LSCRAM_ENB_IC_2                       0x0    /* 0b	[14] */
#define R21_AUX_ADC_EN_IC_2                       0x1    /* 1b	[15] */
#define R21_CHOP_ENB_INT_AMP_IC_2_MASK            0x1
#define R21_CHOP_ENB_INT_AMP_IC_2_SHIFT           0
#define R21_PD_INT_AMP_IC_2_MASK                  0x2
#define R21_PD_INT_AMP_IC_2_SHIFT                 1
#define R21_ADC_NON_OVERLAP_ADJ_IC_2_MASK         0x4
#define R21_ADC_NON_OVERLAP_ADJ_IC_2_SHIFT        2
#define R21_ADC_I_SEL_IC_2_MASK                   0xF8
#define R21_ADC_I_SEL_IC_2_SHIFT                  3
#define R21_ADC_CLK_ADJ_IC_2_MASK                 0x700
#define R21_ADC_CLK_ADJ_IC_2_SHIFT                8
#define R21_LCHOP_PMOS_CTRL_IC_2_MASK             0x1800
#define R21_LCHOP_PMOS_CTRL_IC_2_SHIFT            11
#define R21_LCHOP_AMP_ENB_IC_2_MASK               0x2000
#define R21_LCHOP_AMP_ENB_IC_2_SHIFT              13
#define R21_LSCRAM_ENB_IC_2_MASK                  0x4000
#define R21_LSCRAM_ENB_IC_2_SHIFT                 14
#define R21_AUX_ADC_EN_IC_2_MASK                  0x8000
#define R21_AUX_ADC_EN_IC_2_SHIFT                 15

/* AnalogInterfaceRegister1 (IC 2) */
#define R22_RSWC_DAC_CTRL_IC_2                    0x0    /* 00b	[1:0] */
#define R22_RCLK_SEL_PMOS_IC_2                    0x0    /* 00b	[3:2] */
#define R22_RCLK_SEL_AMP_IC_2                     0x0    /* 00b	[5:4] */
#define R22_RCHOP_PMOS_CTRL_IC_2                  0x0    /* 00b	[7:6] */
#define R22_LCLK_SEL_PMOS_IC_2                    0x0    /* 00b	[9:8] */
#define R22_LSWC_DAC_CTRL_IC_2                    0x0    /* 00b	[11:10] */
#define R22_CLK_SEL_INT_AMP_IC_2                  0x0    /* 00b	[13:12] */
#define R22_RSWC_DAC_CTRL_IC_2_MASK               0x3
#define R22_RSWC_DAC_CTRL_IC_2_SHIFT              0
#define R22_RCLK_SEL_PMOS_IC_2_MASK               0xC
#define R22_RCLK_SEL_PMOS_IC_2_SHIFT              2
#define R22_RCLK_SEL_AMP_IC_2_MASK                0x30
#define R22_RCLK_SEL_AMP_IC_2_SHIFT               4
#define R22_RCHOP_PMOS_CTRL_IC_2_MASK             0xC0
#define R22_RCHOP_PMOS_CTRL_IC_2_SHIFT            6
#define R22_LCLK_SEL_PMOS_IC_2_MASK               0x300
#define R22_LCLK_SEL_PMOS_IC_2_SHIFT              8
#define R22_LSWC_DAC_CTRL_IC_2_MASK               0xC00
#define R22_LSWC_DAC_CTRL_IC_2_SHIFT              10
#define R22_CLK_SEL_INT_AMP_IC_2_MASK             0x3000
#define R22_CLK_SEL_INT_AMP_IC_2_SHIFT            12

/* AnalogInterfaceRegister2 (IC 2) */
#define R23_UNUSED_IC_2                           0x0    /* 0b	[0] */
#define R23_ADC_DITHER_EN_IC_2                    0x0    /* 0b	[1] */
#define R23_XTAL_ENB_IC_2                         0x0    /* 0b	[2] */
#define R23_PLL_PD_IC_2                           0x0    /* 0b	[3] */
#define R23_IBIAS_TO_PIN_IC_2                     0x0    /* 0b	[4] */
#define R23_IBIAS_ADJ_IC_2                        0x0    /* 0000b	[8:5] */
#define R23_VREF_TRIM_IC_2                        0x0    /* 0000b	[12:9] */
#define R23_REF_I_SEL_IC_2                        0x0    /* 0b	[13] */
#define R23_RSCRAM_ENB_IC_2                       0x0    /* 0b	[14] */
#define R23_LCHOP_AMP_ENB_IC_2                    0x0    /* 0b	[15] */
#define R23_UNUSED_IC_2_MASK                      0x1
#define R23_UNUSED_IC_2_SHIFT                     0
#define R23_ADC_DITHER_EN_IC_2_MASK               0x2
#define R23_ADC_DITHER_EN_IC_2_SHIFT              1
#define R23_XTAL_ENB_IC_2_MASK                    0x4
#define R23_XTAL_ENB_IC_2_SHIFT                   2
#define R23_PLL_PD_IC_2_MASK                      0x8
#define R23_PLL_PD_IC_2_SHIFT                     3
#define R23_IBIAS_TO_PIN_IC_2_MASK                0x10
#define R23_IBIAS_TO_PIN_IC_2_SHIFT               4
#define R23_IBIAS_ADJ_IC_2_MASK                   0x1E0
#define R23_IBIAS_ADJ_IC_2_SHIFT                  5
#define R23_VREF_TRIM_IC_2_MASK                   0x1E00
#define R23_VREF_TRIM_IC_2_SHIFT                  9
#define R23_REF_I_SEL_IC_2_MASK                   0x2000
#define R23_REF_I_SEL_IC_2_SHIFT                  13
#define R23_RSCRAM_ENB_IC_2_MASK                  0x4000
#define R23_RSCRAM_ENB_IC_2_SHIFT                 14
#define R23_LCHOP_AMP_ENB_IC_2_MASK               0x8000
#define R23_LCHOP_AMP_ENB_IC_2_SHIFT              15

/* AnalogInterfaceRegister3 (IC 2) */
#define R24_DAC_OFFSET_IC_2                       0x1    /* 01b	[1:0] */
#define R24_DAC_DITHER_ENABLE_IC_2                0x0    /* 0b	[2] */
#define R24_PCTRL_IC_2                            0x0    /* 00b	[4:3] */
#define R24_DAC_PCLK_SEL_IC_2                     0x0    /* 00b	[6:5] */
#define R24_DAC_I_SEL_IC_2                        0x0    /* 0000b	[10:7] */
#define R24_DAC_CLK_SEL_IC_2                      0x0    /* 00b	[12:11] */
#define R24_DAC_CHOP_ENB_IC_2                     0x0    /* 0b	[13] */
#define R24_DAC_DRTZ_IC_2                         0x0    /* 00b	[15:14] */
#define R24_DAC_OFFSET_IC_2_MASK                  0x3
#define R24_DAC_OFFSET_IC_2_SHIFT                 0
#define R24_DAC_DITHER_ENABLE_IC_2_MASK           0x4
#define R24_DAC_DITHER_ENABLE_IC_2_SHIFT          2
#define R24_PCTRL_IC_2_MASK                       0x18
#define R24_PCTRL_IC_2_SHIFT                      3
#define R24_DAC_PCLK_SEL_IC_2_MASK                0x60
#define R24_DAC_PCLK_SEL_IC_2_SHIFT               5
#define R24_DAC_I_SEL_IC_2_MASK                   0x780
#define R24_DAC_I_SEL_IC_2_SHIFT                  7
#define R24_DAC_CLK_SEL_IC_2_MASK                 0x1800
#define R24_DAC_CLK_SEL_IC_2_SHIFT                11
#define R24_DAC_CHOP_ENB_IC_2_MASK                0x2000
#define R24_DAC_CHOP_ENB_IC_2_SHIFT               13
#define R24_DAC_DRTZ_IC_2_MASK                    0xC000
#define R24_DAC_DRTZ_IC_2_SHIFT                   14

#endif
