﻿<?xml version="1.0" encoding="utf-8"?>
<!-- *
 * This software is distributed in the hope that it will be useful,
 * but is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 *
 * This software may only be used to program products purchased from
 * Analog Devices for incorporation by you into audio products that
 * are intended for resale to audio product end users. This software
 * may not be distributed whole or in any part to third parties.
 *
 * Copyright © 2025 Analog Devices, Inc. All rights reserved.
 */
-->
<!--NetList XML file-->
<NetList>
    <IC name=" IC 1 " type="DSPSigma100">
        <Schematic>
            <Algorithm name="ICSigma100In1" friendlyname="170x\140x input " cell="Input1 " location="{X=-12, Y=219} " Growth="10 " GrowthB="0 " FS="48000">
                <Link pin="O_C0_A0_P1_out" dir="out" link="Link36" />
                <Link pin="O_C0_A0_P2_out" dir="out" link="Link37" />
            </Algorithm>
            <Algorithm name="RegReadAlgADCIn1" friendlyname="Auxiliary ADC Input " cell="ADC In1_4 " location="{X=230, Y=84} " Growth="0 " GrowthB="0 " FS="48000">
                <Link pin="O_C41_A0_P1_out" dir="out" link="Link4" />
            </Algorithm>
            <Algorithm name="RegReadAlgADCIn2" friendlyname="Auxiliary ADC Input " cell="ADC In1_2 " location="{X=879, Y=154} " Growth="0 " GrowthB="0 " FS="48000">
                <Link pin="O_C176_A0_P1_out" dir="out" link="Link6" />
            </Algorithm>
            <Algorithm name="RegReadAlgADCIn3" friendlyname="Auxiliary ADC Input " cell="ADC In1_3 " location="{X=882, Y=271} " Growth="0 " GrowthB="0 " FS="48000">
                <Link pin="O_C178_A0_P1_out" dir="out" link="Link5" />
            </Algorithm>
            <Algorithm name="RegReadAlgADCIn4" friendlyname="Auxiliary ADC Input " cell="ADC In1_5 " location="{X=454, Y=368} " Growth="0 " GrowthB="0 " FS="48000">
                <Link pin="O_C216_A0_P1_out" dir="out" link="Link9" />
            </Algorithm>
            <Algorithm name="DCInpAlg1" friendlyname="dc Input " cell="DC1 " location="{X=451, Y=421} " Growth="0 " GrowthB="0 " FS="48000">
                <Link pin="O_C78_A0_P1_out" dir="out" link="Link10" />
            </Algorithm>
            <Algorithm name="MultAlg1" friendlyname="Multiplication " cell="X1 " location="{X=563, Y=395} " Growth="0 " GrowthB="0">
                <Link pin="I_C61_A0_P1_in" dir="in" link="Link9" />
                <Link pin="I_C61_A0_P2_in" dir="in" link="Link10" />
                <Link pin="O_C61_A0_P3_out" dir="out" link="Link11" />
            </Algorithm>
            <Algorithm name="Gain1940AlgNS1" friendlyname="Gain (no slew) " cell="Gain1 " location="{X=88, Y=181} " Growth="0 " GrowthB="0">
                <Link pin="I_C52_A0_P1_in" dir="in" link="Link36" />
                <Link pin="O_C52_A0_P2_out" dir="out" link="Link38" />
            </Algorithm>
            <Algorithm name="Gain1940AlgNS2" friendlyname="Gain (no slew) " cell="Gain1 " location="{X=88, Y=181} " Growth="0 " GrowthB="0">
                <Link pin="I_C52_A1_P1_in" dir="in" link="Link37" />
                <Link pin="O_C52_A1_P2_out" dir="out" link="Link39" />
            </Algorithm>
            <Algorithm name="ExtSWGainDB1" friendlyname="Ext vol (SW slew) " cell="SW vol 1_4 " location="{X=234, Y=134} " Growth="2 " GrowthB="0">
                <Link pin="I_C30_A0_P1_in" dir="in" link="Link4" />
                <Link pin="I_C30_A0_P2_in" dir="in" link="Link38" />
                <Link pin="I_C30_A0_P4_in" dir="in" link="Link39" />
                <Link pin="O_C30_A0_P3_out" dir="out" link="Link32" />
                <Link pin="O_C30_A0_P5_out" dir="out" link="Link33" />
            </Algorithm>
            <Algorithm name="SWGain1940DBAlg1" friendlyname="Gain (RC Slew) " cell="SW vol 1 " location="{X=315, Y=166} " Growth="2 " GrowthB="0">
                <Link pin="I_C262_A0_P1_in" dir="in" link="Link32" />
                <Link pin="I_C262_A0_P3_in" dir="in" link="Link33" />
                <Link pin="O_C262_A0_P2_out" dir="out" link="Link30" />
                <Link pin="O_C262_A0_P4_out" dir="out" link="Link31" />
            </Algorithm>
            <Algorithm name="TwoChannelSingleDetectAlg_Hi_Res1" friendlyname="Stereo Hi Resolution RMS (No Post Gain) " cell="RMS Limiter Hi Res " location="{X=404, Y=187} " Growth="0 " GrowthB="0">
                <Link pin="I_C142_A0_P1_in" dir="in" link="Link30" />
                <Link pin="I_C142_A0_P2_in" dir="in" link="Link31" />
                <Link pin="O_C142_A0_P3_out" dir="out" link="Link7" />
                <Link pin="O_C142_A0_P4_out" dir="out" link="Link8" />
            </Algorithm>
            <Algorithm name="SingleBandSpkrEQAlgDP2" friendlyname="Single Band Automatic Speaker EQ " cell="Auto EQ1_2 " location="{X=541, Y=214} " Growth="0 " GrowthB="0">
                <Link pin="I_C103_A0_P1_in" dir="in" link="Link7" />
                <Link pin="O_C103_A0_P2_out" dir="out" link="Link13" />
            </Algorithm>
            <Algorithm name="SingleBandSpkrEQAlgDP1" friendlyname="Single Band Automatic Speaker EQ " cell="Auto EQ1 " location="{X=543, Y=272} " Growth="0 " GrowthB="0">
                <Link pin="I_C63_A0_P1_in" dir="in" link="Link8" />
                <Link pin="O_C63_A0_P2_out" dir="out" link="Link12" />
            </Algorithm>
            <Algorithm name="ParamToneIndexAlgFix1" friendlyname="Parameter Tone with Index Lookup Tables Fix " cell="2nd Order Filter1 " location="{X=623, Y=295} " Growth="2 " GrowthB="0">
                <Link pin="I_C93_A0_P1_in" dir="in" link="Link11" />
                <Link pin="I_C93_A0_P2_in" dir="in" link="Link13" />
                <Link pin="I_C93_A0_P4_in" dir="in" link="Link12" />
                <Link pin="O_C93_A0_P3_out" dir="out" link="Link14" />
                <Link pin="O_C93_A0_P5_out" dir="out" link="Link15" />
            </Algorithm>
            <Algorithm name="CrossoverFilter2WayAlgDP1" friendlyname="2-Way Crossover Filter - Double Precision " cell="Crossover1 " location="{X=749, Y=286} " Growth="0 " GrowthB="0">
                <Link pin="I_C115_A0_P1_in" dir="in" link="Link14" />
                <Link pin="O_C115_A0_P2_out" dir="out" link="Link16" />
                <Link pin="O_C115_A0_P3_out" dir="out" link="Link18" />
            </Algorithm>
            <Algorithm name="CrossoverFilter2WayAlgDP2" friendlyname="2-Way Crossover Filter - Double Precision " cell="Crossover1 " location="{X=749, Y=286} " Growth="0 " GrowthB="0">
                <Link pin="I_C115_A1_P1_in" dir="in" link="Link15" />
                <Link pin="O_C115_A1_P2_out" dir="out" link="Link17" />
                <Link pin="O_C115_A1_P3_out" dir="out" link="Link19" />
            </Algorithm>
            <Algorithm name="ExtSWGainDB4" friendlyname="Ext vol (SW slew) " cell="SW vol 1_3 " location="{X=899, Y=323} " Growth="2 " GrowthB="0">
                <Link pin="I_C210_A0_P1_in" dir="in" link="Link5" />
                <Link pin="I_C210_A0_P2_in" dir="in" link="Link18" />
                <Link pin="I_C210_A0_P4_in" dir="in" link="Link19" />
                <Link pin="O_C210_A0_P3_out" dir="out" link="Link22" />
                <Link pin="O_C210_A0_P5_out" dir="out" link="Link23" />
            </Algorithm>
            <Algorithm name="ExtSWGainDB3" friendlyname="Ext vol (SW slew) " cell="SW vol 1_2 " location="{X=897, Y=204} " Growth="2 " GrowthB="0">
                <Link pin="I_C204_A0_P1_in" dir="in" link="Link6" />
                <Link pin="I_C204_A0_P2_in" dir="in" link="Link16" />
                <Link pin="I_C204_A0_P4_in" dir="in" link="Link17" />
                <Link pin="O_C204_A0_P3_out" dir="out" link="Link20" />
                <Link pin="O_C204_A0_P5_out" dir="out" link="Link21" />
            </Algorithm>
            <Algorithm name="StereoMixerAlgSlew1" friendlyname="Stereo Mixer SW Slew " cell="St Mixer1 " location="{X=1024, Y=221} " Growth="2 " GrowthB="0">
                <Link pin="I_C175_A0_P3_in" dir="in" link="Link20" />
                <Link pin="I_C175_A0_P4_in" dir="in" link="Link21" />
                <Link pin="I_C175_A0_P5_in" dir="in" link="Link22" />
                <Link pin="I_C175_A0_P6_in" dir="in" link="Link23" />
                <Link pin="O_C175_A0_P1_out" dir="out" link="Link24" />
                <Link pin="O_C175_A0_P2_out" dir="out" link="Link34" />
            </Algorithm>
            <Algorithm name="BassBAlg_stereo1" friendlyname="Stereo Dynamic Bass Boost " cell="Bass Boost1 " location="{X=1264, Y=189} " Growth="0 " GrowthB="0">
                <Link pin="I_C15_A0_P1_in" dir="in" link="Link24" />
                <Link pin="I_C15_A0_P2_in" dir="in" link="Link34" />
                <Link pin="O_C15_A0_P3_out" dir="out" link="Link25" />
                <Link pin="O_C15_A0_P4_out" dir="out" link="Link35" />
            </Algorithm>
            <Algorithm name="CrossoverFilter2WayAlgDP4" friendlyname="2-Way Crossover Filter - Double Precision " cell="1500 " location="{X=1693, Y=183} " Growth="0 " GrowthB="0">
                <Link pin="I_C45_A0_P1_in" dir="in" link="Link25" />
                <Link pin="O_C45_A0_P2_out" dir="out" link="Link26" />
                <Link pin="O_C45_A0_P3_out" dir="out" link="Link27" />
            </Algorithm>
            <Algorithm name="CrossoverFilter2WayAlgDP3" friendlyname="2-Way Crossover Filter - Double Precision " cell="2000 " location="{X=1704, Y=272} " Growth="0 " GrowthB="0">
                <Link pin="I_C14_A0_P1_in" dir="in" link="Link35" />
                <Link pin="O_C14_A0_P2_out" dir="out" link="Link28" />
                <Link pin="O_C14_A0_P3_out" dir="out" link="Link29" />
            </Algorithm>
            <Algorithm name="MuteNoSlewAlg1" friendlyname="No Slew (Standard) " cell="Mute1 " location="{X=2000, Y=23} " Growth="1 " GrowthB="0">
                <Link pin="I_C292_A0_P1_in" dir="in" link="Link26" />
                <Link pin="O_C292_A0_P2_out" dir="out" link="Link0" />
            </Algorithm>
            <Algorithm name="MuteNoSlewAlg2" friendlyname="No Slew (Standard) " cell="Mute2 " location="{X=2000, Y=232} " Growth="1 " GrowthB="0">
                <Link pin="I_C295_A0_P1_in" dir="in" link="Link27" />
                <Link pin="O_C295_A0_P2_out" dir="out" link="Link1" />
            </Algorithm>
            <Algorithm name="MuteNoSlewAlg3" friendlyname="No Slew (Standard) " cell="Mute3 " location="{X=2000, Y=445} " Growth="1 " GrowthB="0">
                <Link pin="I_C298_A0_P1_in" dir="in" link="Link28" />
                <Link pin="O_C298_A0_P2_out" dir="out" link="Link2" />
            </Algorithm>
            <Algorithm name="MuteNoSlewAlg4" friendlyname="No Slew (Standard) " cell="Mute4 " location="{X=2000, Y=669} " Growth="1 " GrowthB="0">
                <Link pin="I_C301_A0_P1_in" dir="in" link="Link29" />
                <Link pin="O_C301_A0_P2_out" dir="out" link="Link3" />
            </Algorithm>
            <Algorithm name="ICSigma100Out1" friendlyname="170x\140x output " cell="Output1 / Lo " location="{X=2086, Y=23} " Growth="0 " GrowthB="0">
                <Link pin="I_C11_A0_P1_in" dir="in" link="Link0" />
            </Algorithm>
            <Algorithm name="ICSigma100Out2" friendlyname="170x\140x output " cell="Output2 / Hi " location="{X=2086, Y=232} " Growth="0 " GrowthB="0">
                <Link pin="I_C13_A0_P1_in" dir="in" link="Link1" />
            </Algorithm>
            <Algorithm name="ICSigma100Out3" friendlyname="170x\140x output " cell="Output3 / Lo " location="{X=2086, Y=445} " Growth="0 " GrowthB="0">
                <Link pin="I_C35_A0_P1_in" dir="in" link="Link2" />
            </Algorithm>
            <Algorithm name="ICSigma100Out4" friendlyname="170x\140x output " cell="Output4 / Hi " location="{X=2086, Y=669} " Growth="0 " GrowthB="0">
                <Link pin="I_C37_A0_P1_in" dir="in" link="Link3" />
            </Algorithm>
        </Schematic>
    </IC>
</NetList>