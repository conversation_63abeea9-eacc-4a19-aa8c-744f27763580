"""
EEPROM Manager für ADAU1701 EEPROM-Programmierung
Verwaltet das Schreiben von EEPROM-Daten über I2C
"""

import time
from typing import Optional, List
try:
    import config
    from hex_parser import SigmaStudioHexParser
except ImportError:
    # Fallback für MicroPython
    pass


class EEPROMManager:
    """Manager für EEPROM-Operationen"""
    
    def __init__(self, i2c_bus, eeprom_address: int = 0x50, page_size: int = 32):
        """
        Initialisiert den EEPROM Manager
        
        Args:
            i2c_bus: I2C Bus Objekt
            eeprom_address: I2C-Adresse des EEPROMs (Standard: 0x50)
            page_size: EEPROM Page-Größe für Schreibvorgänge (Standard: 32)
        """
        self.i2c_bus = i2c_bus
        self.eeprom_address = eeprom_address
        self.page_size = page_size
        self.write_delay_ms = getattr(config, 'EEPROM_WRITE_DELAY_MS', 5)
        
    def write_page(self, address: int, data: bytes) -> bool:
        """
        Schreibt eine Page in das EEPROM
        
        Args:
            address: Start-Adresse im EEPROM
            data: Zu schreibende Daten (max. page_size Bytes)
            
        Returns:
            bool: True wenn erfolgreich
        """
        if len(data) > self.page_size:
            raise ValueError(f"Daten zu groß für eine Page: {len(data)} > {self.page_size}")
            
        try:
            # EEPROM Page Write: Adresse + Daten
            self.i2c_bus.write_addressed_bytes(
                i2c_address=self.eeprom_address,
                sub_address=address,
                bytes_array=data,
                n_sub_address_bytes=2
            )
            
            # Warten bis Write-Cycle abgeschlossen
            time.sleep(self.write_delay_ms / 1000.0)
            
            return True
            
        except Exception as e:
            print(f"EEPROM Write Fehler bei Adresse 0x{address:04X}: {e}")
            return False
    
    def write_data(self, start_address: int, data: bytes, verify: bool = True) -> bool:
        """
        Schreibt Daten in das EEPROM (automatische Page-Aufteilung)
        
        Args:
            start_address: Start-Adresse im EEPROM
            data: Zu schreibende Daten
            verify: Daten nach dem Schreiben verifizieren
            
        Returns:
            bool: True wenn erfolgreich
        """
        if not data:
            return True
            
        print(f"Schreibe {len(data)} Bytes ab Adresse 0x{start_address:04X}")
        
        offset = 0
        while offset < len(data):
            # Aktuelle Adresse berechnen
            current_address = start_address + offset
            
            # Page-Grenze berechnen
            page_start = (current_address // self.page_size) * self.page_size
            bytes_to_page_end = self.page_size - (current_address - page_start)
            
            # Anzahl Bytes für diesen Write bestimmen
            bytes_to_write = min(bytes_to_page_end, len(data) - offset)
            
            # Page schreiben
            page_data = data[offset:offset + bytes_to_write]
            
            print(f"  Page 0x{current_address:04X}: {bytes_to_write} Bytes")
            
            if not self.write_page(current_address, page_data):
                print(f"Fehler beim Schreiben von Page 0x{current_address:04X}")
                return False
            
            offset += bytes_to_write
        
        # Verifizierung
        if verify:
            print("Verifiziere geschriebene Daten...")
            if not self.verify_data(start_address, data):
                print("Verifizierung fehlgeschlagen!")
                return False
            print("Verifizierung erfolgreich!")
        
        return True
    
    def read_data(self, address: int, length: int) -> Optional[bytes]:
        """
        Liest Daten aus dem EEPROM
        
        Args:
            address: Start-Adresse
            length: Anzahl zu lesender Bytes
            
        Returns:
            bytes: Gelesene Daten oder None bei Fehler
        """
        try:
            return self.i2c_bus.read_addressed_bytes(
                i2c_address=self.eeprom_address,
                sub_address=address,
                n_bytes=length,
                n_sub_address_bytes=2
            )
        except Exception as e:
            print(f"EEPROM Read Fehler bei Adresse 0x{address:04X}: {e}")
            return None
    
    def verify_data(self, address: int, expected_data: bytes) -> bool:
        """
        Verifiziert geschriebene Daten
        
        Args:
            address: Start-Adresse
            expected_data: Erwartete Daten
            
        Returns:
            bool: True wenn Daten übereinstimmen
        """
        read_data = self.read_data(address, len(expected_data))
        if read_data is None:
            return False
            
        return bytes(read_data) == expected_data
    
    def erase_chip(self, chip_size: int = 32768) -> bool:
        """
        Löscht das gesamte EEPROM (schreibt 0xFF)
        
        Args:
            chip_size: Größe des EEPROMs in Bytes
            
        Returns:
            bool: True wenn erfolgreich
        """
        print(f"Lösche EEPROM ({chip_size} Bytes)...")
        
        erase_data = b'\xFF' * self.page_size
        
        for address in range(0, chip_size, self.page_size):
            remaining = min(self.page_size, chip_size - address)
            page_data = erase_data[:remaining]
            
            print(f"  Lösche Page 0x{address:04X}")
            
            if not self.write_page(address, page_data):
                print(f"Fehler beim Löschen von Page 0x{address:04X}")
                return False
        
        print("EEPROM erfolgreich gelöscht!")
        return True
    
    def program_hex_file(self, hex_content: str, start_address: int = 0) -> bool:
        """
        Programmiert eine Sigma Studio HEX-Datei ins EEPROM
        
        Args:
            hex_content: Inhalt der HEX-Datei
            start_address: Start-Adresse im EEPROM
            
        Returns:
            bool: True wenn erfolgreich
        """
        try:
            parser = SigmaStudioHexParser()
            eeprom_data = parser.parse_hex_file(hex_content)
            
            if not eeprom_data:
                print("Keine Daten in HEX-Datei gefunden")
                return False
            
            print(f"HEX-Datei geparst: {len(eeprom_data)} Bytes")
            
            return self.write_data(start_address, bytes(eeprom_data))
            
        except Exception as e:
            print(f"Fehler beim Programmieren der HEX-Datei: {e}")
            return False
    
    def dump_eeprom(self, start_address: int = 0, length: int = 256) -> Optional[bytes]:
        """
        Liest einen Bereich des EEPROMs aus (für Debugging)
        
        Args:
            start_address: Start-Adresse
            length: Anzahl Bytes
            
        Returns:
            bytes: EEPROM-Inhalt oder None bei Fehler
        """
        print(f"EEPROM Dump: 0x{start_address:04X} - 0x{start_address + length - 1:04X}")
        
        data = self.read_data(start_address, length)
        if data is None:
            return None
        
        # Hex-Dump ausgeben
        for i in range(0, len(data), 16):
            line_data = data[i:i+16]
            hex_str = ' '.join(f'{b:02X}' for b in line_data)
            ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in line_data)
            print(f"{start_address + i:04X}: {hex_str:<48} {ascii_str}")
        
        return data
