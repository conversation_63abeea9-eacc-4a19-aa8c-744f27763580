#!/usr/bin/env python3
"""
Test-Script für EEPROM-Integration
Testet die EEPROM-Funktionalität des TCPI-Adapters
"""

import time
import sys
from pathlib import Path

# Lokale Imports
try:
    from hex_parser import SigmaStudioHexParser
    from eeprom_manager import EEPROMManager
    from eeprom_programmer import EEPROMProgrammer
except ImportError as e:
    print(f"Import-<PERSON>hler: {e}")
    sys.exit(1)


def test_hex_parser():
    """Testet den HEX-Parser"""
    print("=== Test HEX-Parser ===")
    
    # Test-HEX-Datei erstellen
    test_hex = """
:020000040000FA
:10000000010203040506070809101112131415161F
:10001000171819202122232425262728293031323E
:00000001FF
"""
    
    parser = SigmaStudioHexParser()
    
    try:
        data = parser.parse_hex_file(test_hex)
        print(f"✓ HEX-Parser erfolgreich: {len(data)} Bytes")
        print(f"  Erste 16 Bytes: {data[:16].hex()}")
        return True
    except Exception as e:
        print(f"✗ HEX-Parser <PERSON><PERSON>: {e}")
        return False


def test_eeprom_manager_simulation():
    """Testet den EEPROM-Manager mit simuliertem I2C"""
    print("\n=== Test EEPROM-Manager (Simulation) ===")
    
    class MockI2C:
        """Mock I2C-Bus für Tests"""
        def __init__(self):
            self.memory = {}
        
        def write_addressed_bytes(self, i2c_address, sub_address, bytes_array, n_sub_address_bytes=2):
            print(f"  Mock I2C Write: Addr=0x{i2c_address:02X}, Sub=0x{sub_address:04X}, Data={len(bytes_array)} bytes")
            for i, byte in enumerate(bytes_array):
                self.memory[sub_address + i] = byte
        
        def read_addressed_bytes(self, i2c_address, sub_address, n_bytes, n_sub_address_bytes=2):
            print(f"  Mock I2C Read: Addr=0x{i2c_address:02X}, Sub=0x{sub_address:04X}, Len={n_bytes}")
            result = []
            for i in range(n_bytes):
                result.append(self.memory.get(sub_address + i, 0xFF))
            return bytes(result)
    
    try:
        mock_i2c = MockI2C()
        eeprom = EEPROMManager(mock_i2c, eeprom_address=0x50)
        
        # Test-Daten schreiben
        test_data = b'\x01\x02\x03\x04\x05\x06\x07\x08'
        success = eeprom.write_data(0x0000, test_data, verify=True)
        
        if success:
            print("✓ EEPROM-Manager Test erfolgreich")
            return True
        else:
            print("✗ EEPROM-Manager Test fehlgeschlagen")
            return False
            
    except Exception as e:
        print(f"✗ EEPROM-Manager Fehler: {e}")
        return False


def test_hex_to_eeprom_conversion():
    """Testet die HEX-zu-EEPROM Konvertierung"""
    print("\n=== Test HEX-zu-EEPROM Konvertierung ===")
    
    # Sigma Studio ähnliche HEX-Datei
    sigma_hex = """
:020000040000FA
:20000000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC0
:20002000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA0
:20004000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF80
:20006000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF60
:20008000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF40
:2000A000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF20
:2000C000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00
:2000E000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE0
:20010000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFBF
:00000001FF
"""
    
    try:
        parser = SigmaStudioHexParser()
        eeprom_data = parser.parse_hex_file(sigma_hex)
        
        print(f"✓ Konvertierung erfolgreich: {len(eeprom_data)} Bytes")
        print(f"  Erste 32 Bytes: {eeprom_data[:32].hex()}")
        
        # Prüfen ob Daten korrekt sind
        if len(eeprom_data) >= 0x120:
            print("✓ EEPROM-Daten haben erwartete Größe")
            return True
        else:
            print("✗ EEPROM-Daten zu klein")
            return False
            
    except Exception as e:
        print(f"✗ Konvertierung fehlgeschlagen: {e}")
        return False


def test_programmer_connection(host="*************", port=8086):
    """Testet die Verbindung zum TCPI-Server"""
    print(f"\n=== Test Programmer-Verbindung zu {host}:{port} ===")
    
    programmer = EEPROMProgrammer(host, port)
    
    try:
        if programmer.connect():
            print("✓ Verbindung zum TCPI-Server erfolgreich")
            programmer.disconnect()
            return True
        else:
            print("✗ Verbindung zum TCPI-Server fehlgeschlagen")
            return False
            
    except Exception as e:
        print(f"✗ Verbindungsfehler: {e}")
        return False


def create_test_hex_file():
    """Erstellt eine Test-HEX-Datei"""
    test_hex_content = """
:020000040000FA
:10000000010203040506070809101112131415161F
:10001000171819202122232425262728293031323E
:10002000333435363738394041424344454647485E
:10003000494A4B4C4D4E4F505152535455565758FE
:00000001FF
"""
    
    test_file = Path("test_eeprom.hex")
    with open(test_file, 'w') as f:
        f.write(test_hex_content.strip())
    
    print(f"Test-HEX-Datei erstellt: {test_file}")
    return test_file


def main():
    """Hauptfunktion für Tests"""
    print("EEPROM-Integration Testsuite")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 0
    
    # Test 1: HEX-Parser
    total_tests += 1
    if test_hex_parser():
        tests_passed += 1
    
    # Test 2: EEPROM-Manager
    total_tests += 1
    if test_eeprom_manager_simulation():
        tests_passed += 1
    
    # Test 3: HEX-zu-EEPROM Konvertierung
    total_tests += 1
    if test_hex_to_eeprom_conversion():
        tests_passed += 1
    
    # Test 4: Verbindung (optional, nur wenn Server läuft)
    if len(sys.argv) > 1 and sys.argv[1] == "--test-connection":
        total_tests += 1
        host = sys.argv[2] if len(sys.argv) > 2 else "*************"
        if test_programmer_connection(host):
            tests_passed += 1
    
    # Test-HEX-Datei erstellen
    test_file = create_test_hex_file()
    
    # Ergebnisse
    print(f"\n{'='*50}")
    print(f"Tests abgeschlossen: {tests_passed}/{total_tests} erfolgreich")
    
    if tests_passed == total_tests:
        print("✓ Alle Tests bestanden!")
        print(f"\nNächste Schritte:")
        print(f"1. ESP32 mit TCPI-Server starten")
        print(f"2. EEPROM programmieren: python eeprom_programmer.py --hex-file {test_file}")
        print(f"3. DSP mit Reset aktivieren")
    else:
        print("✗ Einige Tests fehlgeschlagen!")
        sys.exit(1)


if __name__ == "__main__":
    main()
