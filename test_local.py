#!/usr/bin/env python3
"""
Lokaler Test für EEPROM-Funktionalität
Testet die Module ohne ESP32-Hardware
"""

import sys
from pathlib import Path

# Lokale Module importieren
try:
    from eeprom_upy import <PERSON>Hex<PERSON><PERSON><PERSON>, SimpleEEPROMManager, test_hex_parser
    from eeprom_programmer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
except ImportError as e:
    print(f"Import-Fehler: {e}")
    sys.exit(1)


class MockI2C:
    """Mock I2C-Bus für Tests ohne Hardware"""
    
    def __init__(self):
        self.memory = {}
        self.operations = []
    
    def write_addressed_bytes(self, i2c_address, sub_address, bytes_array, n_sub_address_bytes=2):
        operation = f"WRITE: I2C=0x{i2c_address:02X}, Addr=0x{sub_address:04X}, Data={len(bytes_array)} bytes"
        self.operations.append(operation)
        print(f"  {operation}")
        
        # Daten im Mock-Memory speichern
        for i, byte in enumerate(bytes_array):
            self.memory[sub_address + i] = byte
    
    def read_addressed_bytes(self, i2c_address, sub_address, n_bytes, n_sub_address_bytes=2):
        operation = f"READ: I2C=0x{i2c_address:02X}, Addr=0x{sub_address:04X}, Len={n_bytes}"
        self.operations.append(operation)
        print(f"  {operation}")
        
        # Daten aus Mock-Memory lesen
        result = []
        for i in range(n_bytes):
            result.append(self.memory.get(sub_address + i, 0xFF))
        return bytes(result)


def test_hex_parser_detailed():
    """Detaillierter Test des HEX-Parsers"""
    print("=== Test HEX-Parser (Detailliert) ===")
    
    # Test mit echter Sigma Studio HEX-Datei
    hex_file = Path("test_sigma.hex")
    if not hex_file.exists():
        print("test_sigma.hex nicht gefunden!")
        return False
    
    with open(hex_file, 'r') as f:
        hex_content = f.read()
    
    parser = SimpleHexParser()
    
    try:
        data = parser.parse_hex_file(hex_content)
        print(f"✓ HEX-Datei erfolgreich geparst: {len(data)} Bytes")
        
        # Erste und letzte Bytes anzeigen
        if len(data) > 0:
            print(f"  Erste 16 Bytes: {data[:16].hex().upper()}")
            if len(data) > 16:
                print(f"  Letzte 16 Bytes: {data[-16:].hex().upper()}")
        
        return True
        
    except Exception as e:
        print(f"✗ HEX-Parser Fehler: {e}")
        return False


def test_eeprom_manager_detailed():
    """Detaillierter Test des EEPROM-Managers"""
    print("\n=== Test EEPROM-Manager (Detailliert) ===")
    
    mock_i2c = MockI2C()
    eeprom = SimpleEEPROMManager(mock_i2c, eeprom_address=0x50)
    
    try:
        # Test 1: Einzelne Page schreiben
        print("\nTest 1: Einzelne Page schreiben")
        test_data = b'\x01\x02\x03\x04\x05\x06\x07\x08'
        success = eeprom.write_page(0x0000, test_data)
        print(f"  Ergebnis: {'✓ OK' if success else '✗ Fehler'}")
        
        # Test 2: Größere Daten schreiben (mehrere Pages)
        print("\nTest 2: Mehrere Pages schreiben")
        large_data = bytes(range(100))  # 100 Bytes
        success = eeprom.write_data(0x0100, large_data, verify=True)
        print(f"  Ergebnis: {'✓ OK' if success else '✗ Fehler'}")
        
        # Test 3: HEX-Datei programmieren
        print("\nTest 3: HEX-Datei programmieren")
        hex_file = Path("test_sigma.hex")
        if hex_file.exists():
            with open(hex_file, 'r') as f:
                hex_content = f.read()
            
            success = eeprom.program_hex_content(hex_content)
            print(f"  Ergebnis: {'✓ OK' if success else '✗ Fehler'}")
        else:
            print("  test_sigma.hex nicht gefunden - Test übersprungen")
        
        # Statistiken anzeigen
        print(f"\nI2C-Operationen: {len(mock_i2c.operations)}")
        print(f"Memory-Einträge: {len(mock_i2c.memory)}")
        
        return True
        
    except Exception as e:
        print(f"✗ EEPROM-Manager Fehler: {e}")
        return False


def test_programmer_offline():
    """Test des Programmers ohne Netzwerk"""
    print("\n=== Test EEPROM-Programmer (Offline) ===")
    
    programmer = EEPROMProgrammer("127.0.0.1", 8086)
    
    # Test der HEX-Datei-Verarbeitung
    hex_file = Path("test_sigma.hex")
    if not hex_file.exists():
        print("test_sigma.hex nicht gefunden!")
        return False
    
    try:
        with open(hex_file, 'r') as f:
            hex_content = f.read().strip()
        
        print(f"✓ HEX-Datei gelesen: {len(hex_content)} Zeichen")
        
        # Befehl zusammenstellen (ohne zu senden)
        command = f"PROGRAM_HEX:{hex_content}".encode('utf-8')
        print(f"✓ Befehl erstellt: {len(command)} Bytes")
        
        return True
        
    except Exception as e:
        print(f"✗ Programmer Test Fehler: {e}")
        return False


def create_simple_test_hex():
    """Erstellt eine einfache Test-HEX-Datei"""
    simple_hex = """
:020000040000FA
:10000000010203040506070809101112131415161F
:10001000171819202122232425262728293031323E
:10002000333435363738394041424344454647485E
:10003000494A4B4C4D4E4F505152535455565758FE
:00000001FF
""".strip()
    
    with open("simple_test.hex", 'w') as f:
        f.write(simple_hex)
    
    print("Einfache Test-HEX-Datei erstellt: simple_test.hex")


def main():
    """Hauptfunktion für lokale Tests"""
    print("EEPROM-Funktionalität - Lokale Tests")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 0
    
    # Test-HEX-Dateien erstellen
    create_simple_test_hex()
    
    # Test 1: Basis HEX-Parser
    total_tests += 1
    if test_hex_parser():
        tests_passed += 1
    
    # Test 2: Detaillierter HEX-Parser
    total_tests += 1
    if test_hex_parser_detailed():
        tests_passed += 1
    
    # Test 3: EEPROM-Manager
    total_tests += 1
    if test_eeprom_manager_detailed():
        tests_passed += 1
    
    # Test 4: Programmer (offline)
    total_tests += 1
    if test_programmer_offline():
        tests_passed += 1
    
    # Ergebnisse
    print(f"\n{'='*50}")
    print(f"Lokale Tests abgeschlossen: {tests_passed}/{total_tests} erfolgreich")
    
    if tests_passed == total_tests:
        print("✓ Alle lokalen Tests bestanden!")
        print("\nNächste Schritte:")
        print("1. Module auf ESP32 kopieren:")
        print("   - eeprom_upy.py")
        print("   - Modifizierte test_tcpi_upy.py")
        print("   - Erweiterte config.py")
        print("2. ESP32 neu starten")
        print("3. EEPROM-Programmierung testen:")
        print("   python eeprom_programmer.py --hex-file test_sigma.hex --host <ESP32_IP>")
    else:
        print("✗ Einige Tests fehlgeschlagen!")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
